<?php
/**
 * ST Pretty URL Pro Meta Tags Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStMetaTagsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'st_pretty_url_meta_templates';
        $this->className = 'StMetaTagGenerator';
        $this->lang = true;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->context = Context::getContext();

        $this->fields_list = array(
            'id_meta_template' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'page_type' => array(
                'title' => $this->l('Page Type'),
                'width' => 'auto',
                'type' => 'select',
                'list' => array(
                    'product' => 'Product',
                    'category' => 'Category',
                    'cms' => 'CMS',
                    'manufacturer' => 'Manufacturer',
                    'supplier' => 'Supplier'
                ),
                'filter_key' => 'a!page_type'
            ),
            'meta_title' => array(
                'title' => $this->l('Meta Title Template'),
                'width' => 'auto'
            ),
            'active' => array(
                'title' => $this->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'date_add' => array(
                'title' => $this->l('Date Added'),
                'align' => 'center',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            )
        );

        parent::__construct();

        $this->meta_title = $this->l('Meta Tags Templates');
    }

    /**
     * Render form
     */
    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Meta Tag Template'),
                'icon' => 'icon-tags'
            ),
            'input' => array(
                array(
                    'type' => 'select',
                    'label' => $this->l('Page Type'),
                    'name' => 'page_type',
                    'required' => true,
                    'options' => array(
                        'query' => array(
                            array('id' => 'product', 'name' => $this->l('Product')),
                            array('id' => 'category', 'name' => $this->l('Category')),
                            array('id' => 'cms', 'name' => $this->l('CMS')),
                            array('id' => 'manufacturer', 'name' => $this->l('Manufacturer')),
                            array('id' => 'supplier', 'name' => $this->l('Supplier')),
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->l('Select the page type for this template')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Meta Title Template'),
                    'name' => 'meta_title',
                    'lang' => true,
                    'rows' => 3,
                    'cols' => 60,
                    'desc' => $this->l('Template for meta title. Use variables like {product_name}, {category_name}, {shop_name}')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Meta Description Template'),
                    'name' => 'meta_description',
                    'lang' => true,
                    'rows' => 5,
                    'cols' => 60,
                    'desc' => $this->l('Template for meta description. Use variables like {product_description_short}, {category_description}')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Meta Keywords Template'),
                    'name' => 'meta_keywords',
                    'lang' => true,
                    'rows' => 3,
                    'cols' => 60,
                    'desc' => $this->l('Template for meta keywords. Use variables like {product_name}, {category_name}, {manufacturer_name}')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('H1 Tag Template'),
                    'name' => 'h1_tag',
                    'lang' => true,
                    'rows' => 2,
                    'cols' => 60,
                    'desc' => $this->l('Template for H1 tag. Use variables like {product_name}, {category_name}')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        )
                    ),
                    'desc' => $this->l('Enable or disable this template')
                )
            ),
            'submit' => array(
                'title' => $this->l('Save'),
            )
        );

        if (!($obj = $this->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        if ($this->display == 'add' || $this->display == 'edit') {
            $this->content .= $this->renderVariablesHelp();
            $this->content .= $this->renderForm();
        } else {
            $this->content .= $this->renderVariablesHelp();
            $this->content .= $this->renderList();
        }

        $this->context->smarty->assign(array(
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ));
    }

    /**
     * Render variables help
     */
    public function renderVariablesHelp()
    {
        $variables = StMetaTagGenerator::getAvailableVariables();
        
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-info"></i> ' . $this->l('Available Variables') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="alert alert-info">';
        $html .= '<p>' . $this->l('You can use the following variables in your meta tag templates:') . '</p>';
        $html .= '</div>';
        
        $html .= '<div class="row">';
        
        // Common variables
        $html .= '<div class="col-md-6">';
        $html .= '<h4>' . $this->l('Common Variables') . '</h4>';
        $html .= '<table class="table table-striped">';
        foreach ($variables as $var => $desc) {
            $html .= '<tr><td><code>' . $var . '</code></td><td>' . $desc . '</td></tr>';
        }
        $html .= '</table>';
        $html .= '</div>';
        
        // Page-specific variables
        $html .= '<div class="col-md-6">';
        $html .= '<h4>' . $this->l('Page-Specific Variables') . '</h4>';
        
        $page_types = array('product', 'category', 'cms', 'manufacturer', 'supplier');
        foreach ($page_types as $page_type) {
            $specific_vars = StMetaTagGenerator::getAvailableVariables($page_type);
            $html .= '<h5>' . ucfirst($page_type) . ' ' . $this->l('Variables') . '</h5>';
            $html .= '<table class="table table-striped table-condensed">';
            foreach ($specific_vars as $var => $desc) {
                if (!isset($variables[$var])) { // Only show page-specific variables
                    $html .= '<tr><td><code>' . $var . '</code></td><td>' . $desc . '</td></tr>';
                }
            }
            $html .= '</table>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Initialize page header toolbar
     */
    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_template'] = array(
                'href' => self::$currentIndex . '&addst_pretty_url_meta_templates&token=' . $this->token,
                'desc' => $this->l('Add new template', null, null, false),
                'icon' => 'process-icon-new'
            );
        }

        parent::initPageHeaderToolbar();
    }

    /**
     * Process delete
     */
    public function processDelete()
    {
        if (Validate::isLoadedObject($object = $this->loadObject())) {
            if ($object->delete()) {
                $this->confirmations[] = $this->l('Template deleted successfully');
            } else {
                $this->errors[] = $this->l('Failed to delete template');
            }
        } else {
            $this->errors[] = $this->l('Template not found');
        }
    }
}

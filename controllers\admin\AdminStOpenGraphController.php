<?php
/**
 * ST Pretty URL Pro Open Graph Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStOpenGraphController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'st_pretty_url_opengraph';
        $this->className = 'StOpenGraphManager';
		$this->identifier = 'id_opengraph';
        $this->lang = true;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->context = Context::getContext();

		parent::__construct();
		
        $this->fields_list = array(
            'id_opengraph' => array(
                'title' => $this->module->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'page_type' => array(
                'title' => $this->module->l('Page Type'),
                'width' => 'auto',
                'type' => 'select',
                'list' => array(
                    'product' => 'Product',
                    'category' => 'Category',
                    'cms' => 'CMS',
                    'manufacturer' => 'Manufacturer',
                    'supplier' => 'Supplier',
                    'index' => 'Homepage'
                ),
                'filter_key' => 'a!page_type'
            ),
            'og_title' => array(
                'title' => $this->module->l('OG Title Template'),
                'width' => 'auto'
            ),
            'og_type' => array(
                'title' => $this->module->l('OG Type'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'active' => array(
                'title' => $this->module->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'date_add' => array(
                'title' => $this->module->l('Date Added'),
                'align' => 'center',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            )
        );

        

        $this->meta_title = $this->module->l('Open Graph Configuration');
    }

    /**
     * Render form
     */
    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->module->l('Open Graph Configuration'),
                'icon' => 'icon-share-alt'
            ),
            'input' => array(
                array(
                    'type' => 'select',
                    'label' => $this->module->l('Page Type'),
                    'name' => 'page_type',
                    'required' => true,
                    'options' => array(
                        'query' => array(
                            array('id' => 'product', 'name' => $this->module->l('Product')),
                            array('id' => 'category', 'name' => $this->module->l('Category')),
                            array('id' => 'cms', 'name' => $this->module->l('CMS')),
                            array('id' => 'manufacturer', 'name' => $this->module->l('Manufacturer')),
                            array('id' => 'supplier', 'name' => $this->module->l('Supplier')),
                            array('id' => 'index', 'name' => $this->module->l('Homepage')),
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->module->l('Select the page type for this Open Graph configuration')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->module->l('OG Title Template'),
                    'name' => 'og_title',
                    'lang' => true,
                    'rows' => 3,
                    'cols' => 60,
                    'desc' => $this->module->l('Template for Open Graph title. Use variables like {product_name}, {category_name}, {shop_name}')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->module->l('OG Description Template'),
                    'name' => 'og_description',
                    'lang' => true,
                    'rows' => 5,
                    'cols' => 60,
                    'desc' => $this->module->l('Template for Open Graph description. Use variables like {product_description_short}, {category_description}')
                ),
                array(
                    'type' => 'text',
                    'label' => $this->module->l('OG Image URL'),
                    'name' => 'og_image',
                    'size' => 60,
                    'desc' => $this->module->l('Default image URL for Open Graph. Leave empty to use automatic image detection.')
                ),
                array(
                    'type' => 'select',
                    'label' => $this->module->l('OG Type'),
                    'name' => 'og_type',
                    'options' => array(
                        'query' => StOpenGraphManager::getAvailableTypes(),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->module->l('Open Graph object type')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->module->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->module->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->module->l('Disabled')
                        )
                    ),
                    'desc' => $this->module->l('Enable or disable this Open Graph configuration')
                )
            ),
            'submit' => array(
                'title' => $this->module->l('Save'),
            )
        );

        if (!($obj = $this->module->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        if ($this->display == 'add' || $this->display == 'edit') {
            $this->content .= $this->renderVariablesHelp();
            $this->content .= $this->renderForm();
        } else {
            $this->content .= $this->renderVariablesHelp();
            $this->content .= $this->renderList();
            $this->content .= $this->renderTestingTools();
        }

        $this->context->smarty->assign(array(
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ));
    }

    /**
     * Render variables help
     */
    public function renderVariablesHelp()
    {
        $variables = StMetaTagGenerator::getAvailableVariables();
        
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-info"></i> ' . $this->module->l('Available Variables for Open Graph') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="alert alert-info">';
        $html .= '<p>' . $this->module->l('You can use the following variables in your Open Graph templates:') . '</p>';
        $html .= '</div>';
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-12">';
        $html .= '<table class="table table-striped">';
        $html .= '<thead><tr><th>' . $this->module->l('Variable') . '</th><th>' . $this->module->l('Description') . '</th></tr></thead>';
        $html .= '<tbody>';
        foreach ($variables as $var => $desc) {
            $html .= '<tr><td><code>' . $var . '</code></td><td>' . $desc . '</td></tr>';
        }
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Render testing tools
     */
    public function renderTestingTools()
    {
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-wrench"></i> ' . $this->module->l('Open Graph Testing Tools') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-6">';
        $html .= '<h4>' . $this->module->l('Facebook Debugger') . '</h4>';
        $html .= '<p>' . $this->module->l('Test your Open Graph tags with Facebook\'s sharing debugger') . '</p>';
        $html .= '<a href="https://developers.facebook.com/tools/debug/" target="_blank" class="btn btn-primary">' . $this->module->l('Open Facebook Debugger') . '</a>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-6">';
        $html .= '<h4>' . $this->module->l('Twitter Card Validator') . '</h4>';
        $html .= '<p>' . $this->module->l('Validate your Twitter Card implementation') . '</p>';
        $html .= '<a href="https://cards-dev.twitter.com/validator" target="_blank" class="btn btn-info">' . $this->module->l('Open Twitter Validator') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<hr>';
        
        $html .= '<h4>' . $this->module->l('Quick Test') . '</h4>';
        $html .= '<div class="form-group">';
        $html .= '<label>' . $this->module->l('Test URL') . '</label>';
        $html .= '<div class="input-group">';
        $html .= '<input type="text" class="form-control" id="test-url" placeholder="' . $this->module->l('Enter a URL to test') . '" value="' . $this->context->shop->getBaseURL(true) . '">';
        $html .= '<span class="input-group-btn">';
        $html .= '<button class="btn btn-default" type="button" onclick="testOpenGraph()">' . $this->module->l('Test') . '</button>';
        $html .= '</span>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div id="test-results" style="display:none;">';
        $html .= '<h5>' . $this->module->l('Test Results') . '</h5>';
        $html .= '<div id="test-content"></div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<script>';
        $html .= 'function testOpenGraph() {';
        $html .= '  var url = document.getElementById("test-url").value;';
        $html .= '  if (url) {';
        $html .= '    window.open("https://developers.facebook.com/tools/debug/?q=" + encodeURIComponent(url), "_blank");';
        $html .= '  }';
        $html .= '}';
        $html .= '</script>';
        
        return $html;
    }

    /**
     * Initialize page header toolbar
     */
    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_opengraph'] = array(
                'href' => self::$currentIndex . '&addst_pretty_url_opengraph&token=' . $this->token,
                'desc' => $this->module->l('Add new Open Graph config', null, null, false),
                'icon' => 'process-icon-new'
            );
        }

        parent::initPageHeaderToolbar();
    }

    /**
     * Process delete
     */
    public function processDelete()
    {
        if (Validate::isLoadedObject($object = $this->module->loadObject())) {
            if ($object->delete()) {
                $this->confirmations[] = $this->module->l('Open Graph configuration deleted successfully');
            } else {
                $this->errors[] = $this->module->l('Failed to delete Open Graph configuration');
            }
        } else {
            $this->errors[] = $this->module->l('Open Graph configuration not found');
        }
    }
}

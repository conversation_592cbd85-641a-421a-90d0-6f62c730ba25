<?php
/**
 * ST Pretty URL Pro Meta Tag Generator Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StMetaTagGenerator
{
    private $context;
    private $controller;
    private $variables = array();

    public function __construct()
    {
        $this->context = Context::getContext();
        $this->controller = $this->context->controller;
    }

    /**
     * Generate meta tags for current page
     */
    public function generateMetaTags()
    {
        if (!Configuration::get('STPRETTYURLPRO_AUTO_META')) {
            return '';
        }

        $page_type = $this->getPageType();
        $template = $this->getMetaTemplate($page_type);

        if (!$template) {
            return '';
        }

        $this->loadVariables($page_type);
        
        $meta_html = '';
        
        // Generate title
        if (!empty($template['meta_title'])) {
            $title = $this->replaceVariables($template['meta_title']);
            $meta_html .= '<title>' . Tools::safeOutput($title) . '</title>' . "\n";
        }

        // Generate meta description
        if (!empty($template['meta_description'])) {
            $description = $this->replaceVariables($template['meta_description']);
            $meta_html .= '<meta name="description" content="' . Tools::safeOutput($description) . '">' . "\n";
        }

        // Generate meta keywords
        if (!empty($template['meta_keywords'])) {
            $keywords = $this->replaceVariables($template['meta_keywords']);
            $meta_html .= '<meta name="keywords" content="' . Tools::safeOutput($keywords) . '">' . "\n";
        }

        return $meta_html;
    }

    /**
     * Get current page type
     */
    private function getPageType()
    {
        $controller_name = get_class($this->controller);
        
        switch ($controller_name) {
            case 'ProductController':
                return 'product';
            case 'CategoryController':
                return 'category';
            case 'CmsController':
                return 'cms';
            case 'ManufacturerController':
                return 'manufacturer';
            case 'SupplierController':
                return 'supplier';
            case 'IndexController':
                return 'index';
            default:
                return 'default';
        }
    }

    /**
     * Get meta template for page type
     */
    private function getMetaTemplate($page_type)
    {
        $templates = StPrettyUrlConfig::getMetaTemplates($page_type, $this->context->language->id);
        
        if (!empty($templates)) {
            return $templates[0];
        }

        return null;
    }

    /**
     * Load variables for current page
     */
    private function loadVariables($page_type)
    {
        // Common variables
        $this->variables = array(
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{shop_email}' => Configuration::get('PS_SHOP_EMAIL'),
            '{meta_title}' => $this->context->shop->name,
            '{meta_description}' => Configuration::get('PS_META_DESCRIPTION', $this->context->language->id),
            '{meta_keywords}' => Configuration::get('PS_META_KEYWORDS', $this->context->language->id),
            '{current_year}' => date('Y'),
            '{current_month}' => date('F'),
            '{current_date}' => date('Y-m-d'),
            '{language_iso}' => $this->context->language->iso_code,
            '{currency_iso}' => $this->context->currency->iso_code,
        );

        switch ($page_type) {
            case 'product':
                $this->loadProductVariables();
                break;
            case 'category':
                $this->loadCategoryVariables();
                break;
            case 'cms':
                $this->loadCmsVariables();
                break;
            case 'manufacturer':
                $this->loadManufacturerVariables();
                break;
            case 'supplier':
                $this->loadSupplierVariables();
                break;
        }
    }

    /**
     * Load product variables
     */
    private function loadProductVariables()
    {
        if (!isset($this->controller->product) || !Validate::isLoadedObject($this->controller->product)) {
            return;
        }

        $product = $this->controller->product;
        $category = new Category($product->id_category_default, $this->context->language->id);
        $manufacturer = new Manufacturer($product->id_manufacturer, $this->context->language->id);

        $this->variables = array_merge($this->variables, array(
            '{product_name}' => $product->name,
            '{product_description}' => strip_tags($product->description),
            '{product_description_short}' => strip_tags($product->description_short),
            '{product_reference}' => $product->reference,
            '{product_ean13}' => $product->ean13,
            '{product_upc}' => $product->upc,
            '{product_price}' => Product::getPriceStatic($product->id),
            '{product_price_formatted}' => Tools::displayPrice(Product::getPriceStatic($product->id)),
            '{category_name}' => $category->name,
            '{category_description}' => strip_tags($category->description),
            '{manufacturer_name}' => $manufacturer->name,
            '{manufacturer_description}' => strip_tags($manufacturer->description),
            '{product_features}' => $this->getProductFeatures($product->id),
            '{product_tags}' => $this->getProductTags($product->id),
        ));
    }

    /**
     * Load category variables
     */
    private function loadCategoryVariables()
    {
        if (!isset($this->controller->category) || !Validate::isLoadedObject($this->controller->category)) {
            return;
        }

        $category = $this->controller->category;
        $parent_category = new Category($category->id_parent, $this->context->language->id);

        $this->variables = array_merge($this->variables, array(
            '{category_name}' => $category->name,
            '{category_description}' => strip_tags($category->description),
            '{parent_category_name}' => $parent_category->name,
            '{category_products_count}' => $category->getProducts($this->context->language->id, 1, 1, null, null, false, true, false, 1),
            '{category_level}' => $category->level_depth,
        ));
    }

    /**
     * Load CMS variables
     */
    private function loadCmsVariables()
    {
        if (!isset($this->controller->cms) || !Validate::isLoadedObject($this->controller->cms)) {
            return;
        }

        $cms = $this->controller->cms;

        $this->variables = array_merge($this->variables, array(
            '{cms_title}' => $cms->meta_title,
            '{cms_meta_description}' => $cms->meta_description,
            '{cms_meta_keywords}' => $cms->meta_keywords,
            '{cms_content}' => strip_tags($cms->content),
        ));
    }

    /**
     * Load manufacturer variables
     */
    private function loadManufacturerVariables()
    {
        if (!isset($this->controller->manufacturer) || !Validate::isLoadedObject($this->controller->manufacturer)) {
            return;
        }

        $manufacturer = $this->controller->manufacturer;

        $this->variables = array_merge($this->variables, array(
            '{manufacturer_name}' => $manufacturer->name,
            '{manufacturer_description}' => strip_tags($manufacturer->description),
            '{manufacturer_short_description}' => strip_tags($manufacturer->short_description),
        ));
    }

    /**
     * Load supplier variables
     */
    private function loadSupplierVariables()
    {
        if (!isset($this->controller->supplier) || !Validate::isLoadedObject($this->controller->supplier)) {
            return;
        }

        $supplier = $this->controller->supplier;

        $this->variables = array_merge($this->variables, array(
            '{supplier_name}' => $supplier->name,
            '{supplier_description}' => strip_tags($supplier->description),
        ));
    }

    /**
     * Get product features as string
     */
    private function getProductFeatures($id_product)
    {
        $features = Product::getFrontFeaturesStatic($this->context->language->id, $id_product);
        $feature_names = array();

        foreach ($features as $feature) {
            $feature_names[] = $feature['name'] . ': ' . $feature['value'];
        }

        return implode(', ', $feature_names);
    }

    /**
     * Get product tags as string
     */
    private function getProductTags($id_product)
    {
        $tags = Tag::getProductTags($id_product);
        
        if (isset($tags[$this->context->language->id])) {
            return implode(', ', $tags[$this->context->language->id]);
        }

        return '';
    }

    /**
     * Replace variables in template
     */
    private function replaceVariables($template)
    {
        return str_replace(array_keys($this->variables), array_values($this->variables), $template);
    }

    /**
     * Generate H1 tag
     */
    public function generateH1Tag()
    {
        $page_type = $this->getPageType();
        $template = $this->getMetaTemplate($page_type);

        if (!$template || empty($template['h1_tag'])) {
            return '';
        }

        $this->loadVariables($page_type);
        $h1_content = $this->replaceVariables($template['h1_tag']);

        return '<h1>' . Tools::safeOutput($h1_content) . '</h1>';
    }

    /**
     * Get available variables for page type
     */
    public static function getAvailableVariables($page_type = null)
    {
        $common_variables = array(
            '{shop_name}' => 'Shop name',
            '{shop_email}' => 'Shop email',
            '{current_year}' => 'Current year',
            '{current_month}' => 'Current month',
            '{current_date}' => 'Current date',
            '{language_iso}' => 'Language ISO code',
            '{currency_iso}' => 'Currency ISO code',
        );

        $specific_variables = array(
            'product' => array(
                '{product_name}' => 'Product name',
                '{product_description}' => 'Product description',
                '{product_description_short}' => 'Product short description',
                '{product_reference}' => 'Product reference',
                '{product_price}' => 'Product price',
                '{product_price_formatted}' => 'Product price formatted',
                '{category_name}' => 'Category name',
                '{manufacturer_name}' => 'Manufacturer name',
                '{product_features}' => 'Product features',
                '{product_tags}' => 'Product tags',
            ),
            'category' => array(
                '{category_name}' => 'Category name',
                '{category_description}' => 'Category description',
                '{parent_category_name}' => 'Parent category name',
                '{category_products_count}' => 'Number of products in category',
                '{category_level}' => 'Category level',
            ),
            'cms' => array(
                '{cms_title}' => 'CMS title',
                '{cms_meta_description}' => 'CMS meta description',
                '{cms_meta_keywords}' => 'CMS meta keywords',
                '{cms_content}' => 'CMS content',
            ),
            'manufacturer' => array(
                '{manufacturer_name}' => 'Manufacturer name',
                '{manufacturer_description}' => 'Manufacturer description',
                '{manufacturer_short_description}' => 'Manufacturer short description',
            ),
            'supplier' => array(
                '{supplier_name}' => 'Supplier name',
                '{supplier_description}' => 'Supplier description',
            ),
        );

        if ($page_type && isset($specific_variables[$page_type])) {
            return array_merge($common_variables, $specific_variables[$page_type]);
        }

        return $common_variables;
    }
}

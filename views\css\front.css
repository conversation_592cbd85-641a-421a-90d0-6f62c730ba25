/**
 * ST Pretty URL Pro Front-end Styles
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* Debug mode styles */
.stprettyurlpro-debug {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 9999;
    max-width: 300px;
}

.stprettyurlpro-debug h4 {
    margin: 0 0 5px 0;
    color: #fff;
    font-size: 14px;
}

.stprettyurlpro-debug ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.stprettyurlpro-debug li {
    margin: 2px 0;
    font-size: 11px;
}

.stprettyurlpro-debug .url-info {
    color: #90EE90;
}

.stprettyurlpro-debug .redirect-info {
    color: #FFB6C1;
}

.stprettyurlpro-debug .meta-info {
    color: #87CEEB;
}

/* SEO enhancement indicators */
.stprettyurlpro-seo-enhanced {
    position: relative;
}

.stprettyurlpro-seo-enhanced::after {
    content: "✓ SEO Enhanced";
    position: absolute;
    top: -20px;
    right: 0;
    background: #28a745;
    color: white;
    padding: 2px 6px;
    font-size: 10px;
    border-radius: 3px;
    display: none;
}

/* Show SEO indicators in debug mode */
body.stprettyurlpro-debug-mode .stprettyurlpro-seo-enhanced::after {
    display: block;
}

<?php
/**
 * ST Pretty URL Pro URL Cleaner Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StUrlCleaner
{
    private static $instance;
    private $context;
    private $cache = array();

    public function __construct()
    {
        $this->context = Context::getContext();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Clean product URL by removing ID
     */
    public function cleanProductUrl($id_product, $id_product_attribute = null, $id_lang = null, $id_shop = null)
    {
        if (!StPrettyUrlConfig::isIdRemovalEnabled()) {
            return null;
        }

        if ($id_lang === null) {
            $id_lang = $this->context->language->id;
        }

        if ($id_shop === null) {
            $id_shop = $this->context->shop->id;
        }

        $cache_key = 'product_' . $id_product . '_' . $id_product_attribute . '_' . $id_lang . '_' . $id_shop;
        
        if (isset($this->cache[$cache_key])) {
            return $this->cache[$cache_key];
        }

        $product = new Product($id_product, false, $id_lang, $id_shop);
        if (!Validate::isLoadedObject($product)) {
            return null;
        }

        $category = new Category($product->id_category_default, $id_lang);
        $pattern = $this->getUrlPattern('product');

        $variables = array(
            '{product_rewrite}' => $product->link_rewrite,
            '{category_rewrite}' => $category->link_rewrite,
            '{product_id}' => $id_product,
            '{category_id}' => $product->id_category_default,
            '{manufacturer_rewrite}' => $this->getManufacturerRewrite($product->id_manufacturer, $id_lang),
            '{reference}' => $product->reference
        );

        $clean_url = str_replace(array_keys($variables), array_values($variables), $pattern);
        $clean_url = $this->sanitizeUrl($clean_url);

        $this->cache[$cache_key] = $clean_url;
        return $clean_url;
    }

    /**
     * Clean category URL by removing ID
     */
    public function cleanCategoryUrl($id_category, $id_lang = null, $id_shop = null)
    {
        if (!StPrettyUrlConfig::isIdRemovalEnabled()) {
            return null;
        }

        if ($id_lang === null) {
            $id_lang = $this->context->language->id;
        }

        if ($id_shop === null) {
            $id_shop = $this->context->shop->id;
        }

        $cache_key = 'category_' . $id_category . '_' . $id_lang . '_' . $id_shop;
        
        if (isset($this->cache[$cache_key])) {
            return $this->cache[$cache_key];
        }

        $category = new Category($id_category, $id_lang);
        if (!Validate::isLoadedObject($category)) {
            return null;
        }

        $pattern = $this->getUrlPattern('category');
        $parent_categories = $category->getParentsCategories($id_lang);
        $category_path = '';

        // Build category path (excluding root categories)
        foreach (array_reverse($parent_categories) as $parent) {
            if ($parent['id_category'] > 2) { // Skip root and home categories
                $category_path .= $parent['link_rewrite'] . '/';
            }
        }

        $variables = array(
            '{category_rewrite}' => $category->link_rewrite,
            '{category_id}' => $id_category,
            '{category_path}' => rtrim($category_path, '/'),
            '{parent_category}' => $this->getParentCategoryRewrite($category->id_parent, $id_lang)
        );

        $clean_url = str_replace(array_keys($variables), array_values($variables), $pattern);
        $clean_url = $this->sanitizeUrl($clean_url);

        $this->cache[$cache_key] = $clean_url;
        return $clean_url;
    }

    /**
     * Clean CMS URL by removing ID
     */
    public function cleanCmsUrl($id_cms, $id_lang = null, $id_shop = null)
    {
        if (!StPrettyUrlConfig::isIdRemovalEnabled()) {
            return null;
        }

        if ($id_lang === null) {
            $id_lang = $this->context->language->id;
        }

        if ($id_shop === null) {
            $id_shop = $this->context->shop->id;
        }

        $cache_key = 'cms_' . $id_cms . '_' . $id_lang . '_' . $id_shop;
        
        if (isset($this->cache[$cache_key])) {
            return $this->cache[$cache_key];
        }

        $cms = new CMS($id_cms, $id_lang);
        if (!Validate::isLoadedObject($cms)) {
            return null;
        }

        $pattern = $this->getUrlPattern('cms');

        $variables = array(
            '{cms_rewrite}' => $cms->link_rewrite,
            '{cms_id}' => $id_cms,
            '{cms_category}' => $this->getCmsCategoryRewrite($cms->id_cms_category, $id_lang)
        );

        $clean_url = str_replace(array_keys($variables), array_values($variables), $pattern);
        $clean_url = $this->sanitizeUrl($clean_url);

        $this->cache[$cache_key] = $clean_url;
        return $clean_url;
    }

    /**
     * Get URL pattern for page type
     */
    private function getUrlPattern($page_type)
    {
        $patterns = StPrettyUrlConfig::getUrlPatterns($page_type);
        
        if (!empty($patterns)) {
            return $patterns[0]['url_pattern'];
        }

        // Default patterns
        $default_patterns = array(
            'product' => '{category_rewrite}/{product_rewrite}.html',
            'category' => '{category_rewrite}/',
            'cms' => 'content/{cms_rewrite}.html'
        );

        return isset($default_patterns[$page_type]) ? $default_patterns[$page_type] : '';
    }

    /**
     * Get manufacturer rewrite
     */
    private function getManufacturerRewrite($id_manufacturer, $id_lang)
    {
        if (!$id_manufacturer) {
            return '';
        }

        $manufacturer = new Manufacturer($id_manufacturer, $id_lang);
        return Validate::isLoadedObject($manufacturer) ? $manufacturer->name : '';
    }

    /**
     * Get parent category rewrite
     */
    private function getParentCategoryRewrite($id_parent, $id_lang)
    {
        if (!$id_parent || $id_parent <= 2) {
            return '';
        }

        $parent = new Category($id_parent, $id_lang);
        return Validate::isLoadedObject($parent) ? $parent->link_rewrite : '';
    }

    /**
     * Get CMS category rewrite
     */
    private function getCmsCategoryRewrite($id_cms_category, $id_lang)
    {
        if (!$id_cms_category) {
            return '';
        }

        $cms_category = new CMSCategory($id_cms_category, $id_lang);
        return Validate::isLoadedObject($cms_category) ? $cms_category->link_rewrite : '';
    }

    /**
     * Sanitize URL
     */
    private function sanitizeUrl($url)
    {
        // Remove double slashes
        $url = preg_replace('/\/+/', '/', $url);
        
        // Remove leading slash
        $url = ltrim($url, '/');
        
        // Remove trailing slash for files
        if (strpos($url, '.html') !== false || strpos($url, '.htm') !== false) {
            $url = rtrim($url, '/');
        }

        return $url;
    }

    /**
     * Generate clean URL for any object
     */
    public function generateCleanUrl($page_type, $id_object, $id_lang = null, $id_shop = null, $params = array())
    {
        switch ($page_type) {
            case 'product':
                $id_product_attribute = isset($params['id_product_attribute']) ? $params['id_product_attribute'] : null;
                return $this->cleanProductUrl($id_object, $id_product_attribute, $id_lang, $id_shop);
            
            case 'category':
                return $this->cleanCategoryUrl($id_object, $id_lang, $id_shop);
            
            case 'cms':
                return $this->cleanCmsUrl($id_object, $id_lang, $id_shop);
            
            default:
                return null;
        }
    }

    /**
     * Check if URL needs cleaning
     */
    public function needsCleaning($url)
    {
        // Check if URL contains IDs that should be removed
        $patterns = array(
            '/\/\d+-/',  // Product/category IDs like /123-product-name
            '/id_product=\d+/',  // Query parameter IDs
            '/id_category=\d+/',
            '/id_cms=\d+/'
        );

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Clear cache
     */
    public function clearCache()
    {
        $this->cache = array();
    }
}

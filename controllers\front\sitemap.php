<?php
/**
 * ST Pretty URL Pro Sitemap Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StPrettyUrlProSitemapModuleFrontController extends ModuleFrontController
{
    public $ssl = true;

    /**
     * Initialize controller
     */
    public function init()
    {
        parent::init();
        
        // Check if sitemap is enabled
        if (!Configuration::get('STPRETTYURLPRO_SITEMAP_ENABLED')) {
            Tools::redirect('index.php?controller=404');
        }
    }

    /**
     * Process sitemap request
     */
    public function initContent()
    {
        // Set XML content type
        header('Content-Type: application/xml; charset=utf-8');
        
        // Generate sitemap
        $sitemap_generator = new StSitemapGenerator();
        $sitemap_xml = $sitemap_generator->generateSitemap();
        
        // Output sitemap
        echo $sitemap_xml;
        exit;
    }

    /**
     * Set media (disable CSS/JS for XML output)
     */
    public function setMedia()
    {
        // Don't load any CSS or JS for XML output
        return;
    }

    /**
     * Display template (not used for XML output)
     */
    public function display()
    {
        // XML is output directly in initContent
        return;
    }
}

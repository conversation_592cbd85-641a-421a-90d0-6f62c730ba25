<?php
/**
 * ST Pretty URL Pro Open Graph Manager Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StOpenGraphManager extends ObjectModel
{
    public $id_opengraph;
    public $page_type;
    public $og_title;
    public $og_description;
    public $og_image;
    public $og_type;
    public $active;
    public $date_add;
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'st_pretty_url_opengraph',
        'primary' => 'id_opengraph',
        'multilang' => true,
        'fields' => array(
            'page_type' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 50),
            'og_image' => array('type' => self::TYPE_STRING, 'validate' => 'isUrl', 'size' => 255),
            'og_type' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 50),
            'active' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool'),
            'date_add' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
            'date_upd' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
            // Multilang fields
            'og_title' => array('type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'),
            'og_description' => array('type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'),
        ),
    );

    private $context;
    private $controller;
    private $variables = array();

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
        $this->context = Context::getContext();
        $this->controller = $this->context->controller;
    }

    /**
     * Generate Open Graph tags for current page
     */
    public function generateOpenGraphTags()
    {
        if (!Configuration::get('STPRETTYURLPRO_OG_ENABLED')) {
            return '';
        }

        $page_type = $this->getPageType();
        $template = $this->getOpenGraphTemplate($page_type);

        if (!$template) {
            return $this->generateDefaultOpenGraphTags($page_type);
        }

        $this->loadVariables($page_type);
        
        $og_html = '';
        
        // Basic Open Graph tags
        $og_html .= '<meta property="og:site_name" content="' . Tools::safeOutput(Configuration::get('PS_SHOP_NAME')) . '">' . "\n";
        $og_html .= '<meta property="og:url" content="' . Tools::safeOutput($this->getCurrentUrl()) . '">' . "\n";
        
        // Generate title
        if (!empty($template['og_title'])) {
            $title = $this->replaceVariables($template['og_title']);
            $og_html .= '<meta property="og:title" content="' . Tools::safeOutput($title) . '">' . "\n";
        }

        // Generate description
        if (!empty($template['og_description'])) {
            $description = $this->replaceVariables($template['og_description']);
            $og_html .= '<meta property="og:description" content="' . Tools::safeOutput($description) . '">' . "\n";
        }

        // Generate type
        $og_type = !empty($template['og_type']) ? $template['og_type'] : 'website';
        $og_html .= '<meta property="og:type" content="' . Tools::safeOutput($og_type) . '">' . "\n";

        // Generate image
        $og_image = $this->getOpenGraphImage($page_type, $template);
        if ($og_image) {
            $og_html .= '<meta property="og:image" content="' . Tools::safeOutput($og_image) . '">' . "\n";
            $og_html .= '<meta property="og:image:alt" content="' . Tools::safeOutput($this->getImageAlt($page_type)) . '">' . "\n";
        }

        // Twitter Card tags
        $og_html .= $this->generateTwitterCardTags($template);

        return $og_html;
    }

    /**
     * Generate default Open Graph tags when no template is found
     */
    private function generateDefaultOpenGraphTags($page_type)
    {
        $og_html = '';
        $og_html .= '<meta property="og:site_name" content="' . Tools::safeOutput(Configuration::get('PS_SHOP_NAME')) . '">' . "\n";
        $og_html .= '<meta property="og:url" content="' . Tools::safeOutput($this->getCurrentUrl()) . '">' . "\n";
        $og_html .= '<meta property="og:type" content="website">' . "\n";

        switch ($page_type) {
            case 'product':
                if (isset($this->controller->product) && Validate::isLoadedObject($this->controller->product)) {
                    $product = $this->controller->product;
                    $og_html .= '<meta property="og:title" content="' . Tools::safeOutput($product->name) . '">' . "\n";
                    $og_html .= '<meta property="og:description" content="' . Tools::safeOutput(strip_tags($product->description_short)) . '">' . "\n";
                    $og_html .= '<meta property="og:type" content="product">' . "\n";
                    
                    // Product image
                    $image = Image::getCover($product->id);
                    if ($image) {
                        $image_url = $this->context->link->getImageLink($product->link_rewrite, $image['id_image'], 'large_default');
                        $og_html .= '<meta property="og:image" content="' . Tools::safeOutput($image_url) . '">' . "\n";
                    }
                }
                break;

            case 'category':
                if (isset($this->controller->category) && Validate::isLoadedObject($this->controller->category)) {
                    $category = $this->controller->category;
                    $og_html .= '<meta property="og:title" content="' . Tools::safeOutput($category->name) . '">' . "\n";
                    $og_html .= '<meta property="og:description" content="' . Tools::safeOutput(strip_tags($category->description)) . '">' . "\n";
                }
                break;

            case 'cms':
                if (isset($this->controller->cms) && Validate::isLoadedObject($this->controller->cms)) {
                    $cms = $this->controller->cms;
                    $og_html .= '<meta property="og:title" content="' . Tools::safeOutput($cms->meta_title) . '">' . "\n";
                    $og_html .= '<meta property="og:description" content="' . Tools::safeOutput($cms->meta_description) . '">' . "\n";
                }
                break;
        }

        return $og_html;
    }

    /**
     * Generate Twitter Card tags
     */
    private function generateTwitterCardTags($template)
    {
        $twitter_html = '';
        $twitter_html .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
        
        if (!empty($template['og_title'])) {
            $title = $this->replaceVariables($template['og_title']);
            $twitter_html .= '<meta name="twitter:title" content="' . Tools::safeOutput($title) . '">' . "\n";
        }

        if (!empty($template['og_description'])) {
            $description = $this->replaceVariables($template['og_description']);
            $twitter_html .= '<meta name="twitter:description" content="' . Tools::safeOutput($description) . '">' . "\n";
        }

        // Twitter site handle from configuration
        $twitter_site = StPrettyUrlConfig::getConfig('twitter_site_handle');
        if ($twitter_site) {
            $twitter_html .= '<meta name="twitter:site" content="' . Tools::safeOutput($twitter_site) . '">' . "\n";
        }

        return $twitter_html;
    }

    /**
     * Get current page type
     */
    private function getPageType()
    {
        $controller_name = get_class($this->controller);
        
        switch ($controller_name) {
            case 'ProductController':
                return 'product';
            case 'CategoryController':
                return 'category';
            case 'CmsController':
                return 'cms';
            case 'ManufacturerController':
                return 'manufacturer';
            case 'SupplierController':
                return 'supplier';
            case 'IndexController':
                return 'index';
            default:
                return 'default';
        }
    }

    /**
     * Get Open Graph template for page type
     */
    private function getOpenGraphTemplate($page_type)
    {
        $sql = 'SELECT og.*, ogl.* 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_opengraph og
                LEFT JOIN ' . _DB_PREFIX_ . 'st_pretty_url_opengraph_lang ogl 
                ON (og.id_opengraph = ogl.id_opengraph AND ogl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE og.page_type = "' . pSQL($page_type) . '" AND og.active = 1
                ORDER BY og.id_opengraph ASC
                LIMIT 1';

        return Db::getInstance()->getRow($sql);
    }

    /**
     * Load variables for current page (reuse from StMetaTagGenerator)
     */
    private function loadVariables($page_type)
    {
        $meta_generator = new StMetaTagGenerator();
        // Use reflection to access private method
        $reflection = new ReflectionClass($meta_generator);
        $method = $reflection->getMethod('loadVariables');
        $method->setAccessible(true);
        $method->invoke($meta_generator, $page_type);
        
        // Get variables from meta generator
        $variables_property = $reflection->getProperty('variables');
        $variables_property->setAccessible(true);
        $this->variables = $variables_property->getValue($meta_generator);
    }

    /**
     * Replace variables in template
     */
    private function replaceVariables($template)
    {
        return str_replace(array_keys($this->variables), array_values($this->variables), $template);
    }

    /**
     * Get Open Graph image
     */
    private function getOpenGraphImage($page_type, $template)
    {
        // Check if template has specific image
        if (!empty($template['og_image'])) {
            return $template['og_image'];
        }

        // Get page-specific image
        switch ($page_type) {
            case 'product':
                if (isset($this->controller->product) && Validate::isLoadedObject($this->controller->product)) {
                    $product = $this->controller->product;
                    $image = Image::getCover($product->id);
                    if ($image) {
                        return $this->context->link->getImageLink($product->link_rewrite, $image['id_image'], 'large_default');
                    }
                }
                break;

            case 'category':
                if (isset($this->controller->category) && Validate::isLoadedObject($this->controller->category)) {
                    // Category image if available
                    $category = $this->controller->category;
                    if ($category->id_image) {
                        return $this->context->link->getCatImageLink($category->link_rewrite, $category->id_image, 'category_default');
                    }
                }
                break;
        }

        // Default shop logo
        $logo = Configuration::get('PS_LOGO');
        if ($logo) {
            return $this->context->shop->getBaseURL(true) . 'img/' . $logo;
        }

        return null;
    }

    /**
     * Get image alt text
     */
    private function getImageAlt($page_type)
    {
        switch ($page_type) {
            case 'product':
                if (isset($this->controller->product) && Validate::isLoadedObject($this->controller->product)) {
                    return $this->controller->product->name;
                }
                break;

            case 'category':
                if (isset($this->controller->category) && Validate::isLoadedObject($this->controller->category)) {
                    return $this->controller->category->name;
                }
                break;

            case 'cms':
                if (isset($this->controller->cms) && Validate::isLoadedObject($this->controller->cms)) {
                    return $this->controller->cms->meta_title;
                }
                break;
        }

        return Configuration::get('PS_SHOP_NAME');
    }

    /**
     * Get current URL
     */
    private function getCurrentUrl()
    {
        $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https://' : 'http://';
        return $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    }

    /**
     * Get all Open Graph configurations
     */
    public static function getAllConfigurations($active_only = false)
    {
        $sql = 'SELECT og.*, ogl.* 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_opengraph og
                LEFT JOIN ' . _DB_PREFIX_ . 'st_pretty_url_opengraph_lang ogl 
                ON (og.id_opengraph = ogl.id_opengraph AND ogl.id_lang = ' . (int)Context::getContext()->language->id . ')';

        if ($active_only) {
            $sql .= ' WHERE og.active = 1';
        }

        $sql .= ' ORDER BY og.page_type, og.id_opengraph';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get available Open Graph types
     */
    public static function getAvailableTypes()
    {
        return array(
            'website' => 'Website',
            'article' => 'Article',
            'product' => 'Product',
            'profile' => 'Profile',
            'book' => 'Book',
            'music.song' => 'Music Song',
            'music.album' => 'Music Album',
            'video.movie' => 'Video Movie',
            'video.episode' => 'Video Episode',
            'video.tv_show' => 'TV Show',
            'video.other' => 'Video Other'
        );
    }
}

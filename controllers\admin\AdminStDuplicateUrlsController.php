<?php
/**
 * ST Pretty URL Pro Duplicate URLs Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStDuplicateUrlsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'st_pretty_url_duplicates';
        $this->className = 'StDuplicateUrlDetector';
        $this->lang = false;
        $this->addRowAction('view');
        $this->context = Context::getContext();

        $this->bulk_actions = array(
            'resolve' => array(
                'text' => $this->l('Mark as resolved'),
                'icon' => 'icon-check'
            ),
            'ignore' => array(
                'text' => $this->l('Mark as ignored'),
                'icon' => 'icon-eye-slash'
            ),
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected items?')
            )
        );

        $this->fields_list = array(
            'id_duplicate' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'url' => array(
                'title' => $this->l('URL'),
                'width' => 'auto'
            ),
            'page_type' => array(
                'title' => $this->l('Type'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'type' => 'select',
                'list' => array(
                    'product' => 'Product',
                    'category' => 'Category',
                    'cms' => 'CMS'
                ),
                'filter_key' => 'a!page_type'
            ),
            'object_id' => array(
                'title' => $this->l('Object ID'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'duplicate_count' => array(
                'title' => $this->l('Count'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'status' => array(
                'title' => $this->l('Status'),
                'align' => 'center',
                'class' => 'fixed-width-sm',
                'type' => 'select',
                'list' => array(
                    'pending' => $this->l('Pending'),
                    'resolved' => $this->l('Resolved'),
                    'ignored' => $this->l('Ignored')
                ),
                'filter_key' => 'a!status',
                'callback' => 'displayStatus'
            ),
            'date_add' => array(
                'title' => $this->l('Date Added'),
                'align' => 'center',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            )
        );

        parent::__construct();

        $this->meta_title = $this->l('Duplicate URLs');
    }

    /**
     * Display status with color coding
     */
    public function displayStatus($value, $row)
    {
        $colors = array(
            'pending' => 'warning',
            'resolved' => 'success',
            'ignored' => 'default'
        );

        $color = isset($colors[$value]) ? $colors[$value] : 'default';
        $label = $value;

        switch ($value) {
            case 'pending':
                $label = $this->l('Pending');
                break;
            case 'resolved':
                $label = $this->l('Resolved');
                break;
            case 'ignored':
                $label = $this->l('Ignored');
                break;
        }

        return '<span class="label label-' . $color . '">' . $label . '</span>';
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        if ($this->display == 'view') {
            $this->content .= $this->renderView();
        } else {
            $this->content .= $this->renderScanForm();
            $this->content .= $this->renderStatistics();
            $this->content .= $this->renderList();
        }

        $this->context->smarty->assign(array(
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ));
    }

    /**
     * Render scan form
     */
    public function renderScanForm()
    {
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'scan_duplicates';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = 'id_scan';
        $helper->submit_action = 'submitScanDuplicates';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminStDuplicateUrls', false);
        $helper->token = Tools::getAdminTokenLite('AdminStDuplicateUrls');

        $helper->tpl_vars = array(
            'fields_value' => array(
                'page_type' => 'all'
            ),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        $form = array(
            'form' => array(
                'legend' => array(
                    'title' => $this->l('Scan for Duplicate URLs'),
                    'icon' => 'icon-search',
                ),
                'input' => array(
                    array(
                        'type' => 'select',
                        'label' => $this->l('Page Type'),
                        'name' => 'page_type',
                        'options' => array(
                            'query' => array(
                                array('id' => 'all', 'name' => $this->l('All Types')),
                                array('id' => 'product', 'name' => $this->l('Products')),
                                array('id' => 'category', 'name' => $this->l('Categories')),
                                array('id' => 'cms', 'name' => $this->l('CMS Pages')),
                            ),
                            'id' => 'id',
                            'name' => 'name'
                        ),
                        'desc' => $this->l('Select the type of pages to scan for duplicates')
                    ),
                ),
                'submit' => array(
                    'title' => $this->l('Start Scan'),
                    'class' => 'btn btn-primary'
                ),
            ),
        );

        return $helper->generateForm(array($form));
    }

    /**
     * Render statistics
     */
    public function renderStatistics()
    {
        $stats = StDuplicateUrlDetector::getDuplicateStats();

        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-bar-chart"></i> ' . $this->l('Statistics') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-3">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-primary">';
        $html .= '<span class="kpi-title">' . $this->l('Total Duplicates') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['total'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-3">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-warning">';
        $html .= '<span class="kpi-title">' . $this->l('Pending') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['pending'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-3">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-success">';
        $html .= '<span class="kpi-title">' . $this->l('Resolved') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['resolved'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-3">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-default">';
        $html .= '<span class="kpi-title">' . $this->l('Ignored') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['ignored'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        if (!empty($stats['by_type'])) {
            $html .= '<h4>' . $this->l('By Type') . '</h4>';
            $html .= '<div class="row">';
            foreach ($stats['by_type'] as $type => $count) {
                $html .= '<div class="col-md-4">';
                $html .= '<strong>' . ucfirst($type) . ':</strong> ' . $count;
                $html .= '</div>';
            }
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Render view for single duplicate
     */
    public function renderView()
    {
        $duplicate = new StDuplicateUrlDetector((int)Tools::getValue('id_duplicate'));
        if (!Validate::isLoadedObject($duplicate)) {
            $this->errors[] = $this->l('Duplicate not found');
            return '';
        }

        $object_details = $duplicate->getObjectDetails();

        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-eye"></i> ' . $this->l('Duplicate URL Details') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<dl class="dl-horizontal">';
        $html .= '<dt>' . $this->l('URL') . ':</dt>';
        $html .= '<dd>' . $duplicate->url . '</dd>';
        $html .= '<dt>' . $this->l('Page Type') . ':</dt>';
        $html .= '<dd>' . ucfirst($duplicate->page_type) . '</dd>';
        $html .= '<dt>' . $this->l('Object ID') . ':</dt>';
        $html .= '<dd>' . $duplicate->object_id . '</dd>';
        $html .= '<dt>' . $this->l('Language') . ':</dt>';
        $html .= '<dd>' . Language::getIsoById($duplicate->id_lang) . '</dd>';
        $html .= '<dt>' . $this->l('Duplicate Count') . ':</dt>';
        $html .= '<dd>' . $duplicate->duplicate_count . '</dd>';
        $html .= '<dt>' . $this->l('Status') . ':</dt>';
        $html .= '<dd>' . $this->displayStatus($duplicate->status, array()) . '</dd>';
        
        if (!empty($object_details)) {
            $html .= '<dt>' . $this->l('Object Name') . ':</dt>';
            $html .= '<dd>' . (isset($object_details['name']) ? $object_details['name'] : '-') . '</dd>';
            if (isset($object_details['reference'])) {
                $html .= '<dt>' . $this->l('Reference') . ':</dt>';
                $html .= '<dd>' . $object_details['reference'] . '</dd>';
            }
        }
        
        $html .= '</dl>';
        
        // Action buttons
        $html .= '<div class="btn-group">';
        $html .= '<a href="' . self::$currentIndex . '&updateStatus&id_duplicate=' . $duplicate->id . '&status=resolved&token=' . $this->token . '" class="btn btn-success">' . $this->l('Mark as Resolved') . '</a>';
        $html .= '<a href="' . self::$currentIndex . '&updateStatus&id_duplicate=' . $duplicate->id . '&status=ignored&token=' . $this->token . '" class="btn btn-default">' . $this->l('Mark as Ignored') . '</a>';
        $html .= '<a href="' . self::$currentIndex . '&token=' . $this->token . '" class="btn btn-primary">' . $this->l('Back to List') . '</a>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Process scan for duplicates
     */
    public function processScanDuplicates()
    {
        $page_type = Tools::getValue('page_type');
        if ($page_type === 'all') {
            $page_type = null;
        }

        $duplicates_found = StDuplicateUrlDetector::scanForDuplicates($page_type);
        
        if ($duplicates_found > 0) {
            $this->confirmations[] = sprintf($this->l('%d duplicate URLs found'), $duplicates_found);
        } else {
            $this->confirmations[] = $this->l('No duplicate URLs found');
        }
    }

    /**
     * Process update status
     */
    public function processUpdateStatus()
    {
        $id_duplicate = (int)Tools::getValue('id_duplicate');
        $status = Tools::getValue('status');

        if (StDuplicateUrlDetector::updateStatus($id_duplicate, $status)) {
            $this->confirmations[] = $this->l('Status updated successfully');
        } else {
            $this->errors[] = $this->l('Failed to update status');
        }
    }

    /**
     * Process bulk actions
     */
    public function processBulkResolve()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $updated = 0;
            foreach ($this->boxes as $id_duplicate) {
                if (StDuplicateUrlDetector::updateStatus($id_duplicate, 'resolved')) {
                    $updated++;
                }
            }
            $this->confirmations[] = sprintf($this->l('%d items marked as resolved'), $updated);
        }
    }

    /**
     * Process bulk ignore
     */
    public function processBulkIgnore()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $updated = 0;
            foreach ($this->boxes as $id_duplicate) {
                if (StDuplicateUrlDetector::updateStatus($id_duplicate, 'ignored')) {
                    $updated++;
                }
            }
            $this->confirmations[] = sprintf($this->l('%d items marked as ignored'), $updated);
        }
    }

    /**
     * Post process
     */
    public function postProcess()
    {
        if (Tools::isSubmit('submitScanDuplicates')) {
            $this->processScanDuplicates();
        } elseif (Tools::getValue('updateStatus')) {
            $this->processUpdateStatus();
        }

        return parent::postProcess();
    }

    /**
     * Initialize page header toolbar
     */
    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['scan'] = array(
                'href' => '#',
                'desc' => $this->l('Scan for duplicates', null, null, false),
                'icon' => 'process-icon-refresh',
                'js' => 'onclick="$(\'#scan_duplicates_form\').submit(); return false;"'
            );
        }

        parent::initPageHeaderToolbar();
    }
}

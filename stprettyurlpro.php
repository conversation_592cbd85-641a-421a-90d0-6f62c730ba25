<?php
/**
 * ST Pretty URL Pro Module
 * 
 * <AUTHOR>
 * @copyright 2024 
 * @license   Academic Free License (AFL 3.0)
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/classes/StPrettyUrlConfig.php';
require_once dirname(__FILE__) . '/classes/StUrlCleaner.php';
require_once dirname(__FILE__) . '/classes/StRedirectManager.php';
require_once dirname(__FILE__) . '/classes/StMetaTagGenerator.php';
require_once dirname(__FILE__) . '/classes/StSitemapGenerator.php';
require_once dirname(__FILE__) . '/classes/StDuplicateUrlDetector.php';
require_once dirname(__FILE__) . '/classes/StOpenGraphManager.php';

class StPrettyUrlPro extends Module
{
    protected $config_form = false;
    
    public function __construct()
    {
        $this->name = 'stprettyurlpro';
        $this->tab = 'seo';
        $this->version = '1.0.0';
        $this->author = 'Sathi';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = array('min' => '1.7', 'max' => _PS_VERSION_);
        $this->bootstrap = true;
        
        parent::__construct();
        
        $this->displayName = $this->l('Pretty URL Pro');
        $this->description = $this->l('Advanced URL management, SEO optimization, and redirect management for PrestaShop');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall this module?');
        
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            $this->warning = $this->l('No name provided');
        }
    }
    
    /**
     * Install module
     */
    public function install()
    {
        include(dirname(__FILE__) . '/sql/install.php');
        
        return parent::install() &&
            $this->registerHook('header') &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('actionDispatcher') &&
            $this->registerHook('actionProductSave') &&
            $this->registerHook('actionCategorySave') &&
            $this->registerHook('actionCMSPageSave') &&
            $this->registerHook('actionObjectLanguageAddAfter') &&
            $this->registerHook('actionObjectLanguageDeleteAfter') &&
            $this->registerHook('moduleRoutes') &&
            $this->installTabs() &&
            $this->installConfiguration();
    }
    
    /**
     * Uninstall module
     */
    public function uninstall()
    {
        include(dirname(__FILE__) . '/sql/uninstall.php');
        
        return $this->uninstallTabs() &&
            $this->uninstallConfiguration() &&
            parent::uninstall();
    }
    
    /**
     * Install admin tabs
     */
    private function installTabs()
    {
        $parent_tab = new Tab();
        $parent_tab->active = 1;
        $parent_tab->class_name = 'AdminStPrettyUrlPro';
        $parent_tab->name = array();
        foreach (Language::getLanguages(true) as $lang) {
            $parent_tab->name[$lang['id_lang']] = 'ST Pretty URL Pro';
        }
        $parent_tab->id_parent = (int)Tab::getIdFromClassName('AdminParentModulesSf');
        $parent_tab->module = $this->name;
        $parent_tab->add();
        
        $tabs = array(
            array(
                'class_name' => 'AdminStPrettyUrlProConfig',
                'name' => 'Configuration',
                'parent_class' => 'AdminStPrettyUrlPro'
            ),
            array(
                'class_name' => 'AdminStRedirectManager',
                'name' => 'Redirect Manager',
                'parent_class' => 'AdminStPrettyUrlPro'
            ),
            array(
                'class_name' => 'AdminStMetaTags',
                'name' => 'Meta Tags',
                'parent_class' => 'AdminStPrettyUrlPro'
            ),
            array(
                'class_name' => 'AdminStSitemap',
                'name' => 'Sitemap',
                'parent_class' => 'AdminStPrettyUrlPro'
            ),
            array(
                'class_name' => 'AdminStDuplicateUrls',
                'name' => 'Duplicate URLs',
                'parent_class' => 'AdminStPrettyUrlPro'
            ),
            array(
                'class_name' => 'AdminStOpenGraph',
                'name' => 'Open Graph',
                'parent_class' => 'AdminStPrettyUrlPro'
            )
        );
        
        foreach ($tabs as $tab_data) {
            $tab = new Tab();
            $tab->active = 1;
            $tab->class_name = $tab_data['class_name'];
            $tab->name = array();
            foreach (Language::getLanguages(true) as $lang) {
                $tab->name[$lang['id_lang']] = $tab_data['name'];
            }
            $tab->id_parent = (int)Tab::getIdFromClassName($tab_data['parent_class']);
            $tab->module = $this->name;
            $tab->add();
        }
        
        return true;
    }
    
    /**
     * Uninstall admin tabs
     */
    private function uninstallTabs()
    {
        $tab_classes = array(
            'AdminStPrettyUrlPro',
            'AdminStPrettyUrlProConfig',
            'AdminStRedirectManager',
            'AdminStMetaTags',
            'AdminStSitemap',
            'AdminStDuplicateUrls',
            'AdminStOpenGraph'
        );
        
        foreach ($tab_classes as $class_name) {
            $id_tab = (int)Tab::getIdFromClassName($class_name);
            if ($id_tab) {
                $tab = new Tab($id_tab);
                $tab->delete();
            }
        }
        
        return true;
    }
    
    /**
     * Install default configuration
     */
    private function installConfiguration()
    {
        $default_config = array(
            'STPRETTYURLPRO_ENABLED' => 1,
            'STPRETTYURLPRO_REMOVE_IDS' => 1,
            'STPRETTYURLPRO_AUTO_REDIRECT' => 1,
            'STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE' => 301,
            'STPRETTYURLPRO_AUTO_META' => 1,
            'STPRETTYURLPRO_SITEMAP_ENABLED' => 1,
            'STPRETTYURLPRO_ROBOTS_ENABLED' => 1,
            'STPRETTYURLPRO_OG_ENABLED' => 1,
            'STPRETTYURLPRO_DUPLICATE_CHECK' => 1,
            'STPRETTYURLPRO_CACHE_ENABLED' => 1,
            'STPRETTYURLPRO_DEBUG_MODE' => 0
        );
        
        foreach ($default_config as $key => $value) {
            Configuration::updateValue($key, $value);
        }
        
        return true;
    }
    
    /**
     * Uninstall configuration
     */
    private function uninstallConfiguration()
    {
        $config_keys = array(
            'STPRETTYURLPRO_ENABLED',
            'STPRETTYURLPRO_REMOVE_IDS',
            'STPRETTYURLPRO_AUTO_REDIRECT',
            'STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE',
            'STPRETTYURLPRO_AUTO_META',
            'STPRETTYURLPRO_SITEMAP_ENABLED',
            'STPRETTYURLPRO_ROBOTS_ENABLED',
            'STPRETTYURLPRO_OG_ENABLED',
            'STPRETTYURLPRO_DUPLICATE_CHECK',
            'STPRETTYURLPRO_CACHE_ENABLED',
            'STPRETTYURLPRO_DEBUG_MODE'
        );
        
        foreach ($config_keys as $key) {
            Configuration::deleteByName($key);
        }
        
        return true;
    }

    /**
     * Hook: header
     */
    public function hookHeader()
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        $this->context->controller->addCSS($this->_path . 'views/css/front.css');
        $this->context->controller->addJS($this->_path . 'views/js/front.js');

        // Add Open Graph meta tags
        if (Configuration::get('STPRETTYURLPRO_OG_ENABLED')) {
            $og_manager = new StOpenGraphManager();
            return $og_manager->generateOpenGraphTags();
        }
    }

    /**
     * Hook: displayHeader
     */
    public function hookDisplayHeader()
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        // Auto generate meta tags
        if (Configuration::get('STPRETTYURLPRO_AUTO_META')) {
            $meta_generator = new StMetaTagGenerator();
            return $meta_generator->generateMetaTags();
        }
    }

    /**
     * Hook: actionDispatcher
     */
    public function hookActionDispatcher($params)
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        $redirect_manager = new StRedirectManager();
        $redirect_manager->handleRedirects();
    }

    /**
     * Hook: moduleRoutes
     */
    public function hookModuleRoutes()
    {
        return array(
            'module-stprettyurlpro-sitemap' => array(
                'controller' => 'sitemap',
                'rule' => 'sitemap.xml',
                'keywords' => array(),
                'params' => array(
                    'fc' => 'module',
                    'module' => 'stprettyurlpro',
                    'controller' => 'sitemap'
                )
            ),
            'module-stprettyurlpro-robots' => array(
                'controller' => 'robots',
                'rule' => 'robots.txt',
                'keywords' => array(),
                'params' => array(
                    'fc' => 'module',
                    'module' => 'stprettyurlpro',
                    'controller' => 'robots'
                )
            )
        );
    }

    /**
     * Hook: actionProductSave
     */
    public function hookActionProductSave($params)
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        // Clear cache and regenerate URLs
        $this->clearCache();
    }

    /**
     * Hook: actionCategorySave
     */
    public function hookActionCategorySave($params)
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        // Clear cache and regenerate URLs
        $this->clearCache();
    }

    /**
     * Hook: actionCMSPageSave
     */
    public function hookActionCMSPageSave($params)
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        // Clear cache and regenerate URLs
        $this->clearCache();
    }

    /**
     * Hook: actionObjectLanguageAddAfter
     */
    public function hookActionObjectLanguageAddAfter($params)
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        // Clear cache when new language is added
        $this->clearCache();

        // Regenerate URL patterns for new language if needed
        if (isset($params['object']) && $params['object'] instanceof Language) {
            $this->regenerateUrlsForLanguage($params['object']->id);
        }
    }

    /**
     * Hook: actionObjectLanguageDeleteAfter
     */
    public function hookActionObjectLanguageDeleteAfter($params)
    {
        if (!Configuration::get('STPRETTYURLPRO_ENABLED')) {
            return;
        }

        // Clear cache when language is deleted
        $this->clearCache();

        // Clean up language-specific data if needed
        if (isset($params['object']) && $params['object'] instanceof Language) {
            $this->cleanupLanguageData($params['object']->id);
        }
    }

    /**
     * Clear module cache
     */
    private function clearCache()
    {
        if (Configuration::get('STPRETTYURLPRO_CACHE_ENABLED')) {
            Cache::clean('stprettyurlpro_*');
        }
    }

    /**
     * Regenerate URLs for a specific language
     */
    private function regenerateUrlsForLanguage($id_lang)
    {
        // This method can be implemented to regenerate URL patterns
        // when a new language is added to the store
        // For now, we just clear the cache
        $this->clearCache();
    }

    /**
     * Clean up language-specific data
     */
    private function cleanupLanguageData($id_lang)
    {
        // Clean up meta templates for deleted language
        $sql = 'DELETE FROM ' . _DB_PREFIX_ . 'st_pretty_url_meta_templates_lang
                WHERE id_lang = ' . (int)$id_lang;
        Db::getInstance()->execute($sql);

        // Clean up Open Graph data for deleted language
        $sql = 'DELETE FROM ' . _DB_PREFIX_ . 'st_pretty_url_opengraph_lang
                WHERE id_lang = ' . (int)$id_lang;
        Db::getInstance()->execute($sql);

        // Clean up duplicate URL data for deleted language
        $sql = 'DELETE FROM ' . _DB_PREFIX_ . 'st_pretty_url_duplicates
                WHERE id_lang = ' . (int)$id_lang;
        Db::getInstance()->execute($sql);
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        if (((bool)Tools::isSubmit('submitStPrettyUrlProModule')) == true) {
            $this->postProcess();
        }

        $this->context->smarty->assign('module_dir', $this->_path);

        $output = $this->context->smarty->fetch($this->local_path . 'views/templates/admin/configure.tpl');

        return $output . $this->renderForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function renderForm()
    {
        $helper = new HelperForm();

        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitStPrettyUrlProModule';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            . '&configure=' . $this->name . '&tab_module=' . $this->tab . '&module_name=' . $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = array(
            'fields_value' => $this->getConfigFormValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        return $helper->generateForm(array($this->getConfigForm()));
    }

    /**
     * Create the structure of your form.
     */
    protected function getConfigForm()
    {
        return array(
            'form' => array(
                'legend' => array(
                    'title' => $this->l('Settings'),
                    'icon' => 'icon-cogs',
                ),
                'input' => array(
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Enable Module'),
                        'name' => 'STPRETTYURLPRO_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->l('Enable or disable the module functionality'),
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ),
                            array(
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            )
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Remove IDs from URLs'),
                        'name' => 'STPRETTYURLPRO_REMOVE_IDS',
                        'is_bool' => true,
                        'desc' => $this->l('Remove product and category IDs from URLs'),
                        'values' => array(
                            array(
                                'id' => 'remove_ids_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ),
                            array(
                                'id' => 'remove_ids_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            )
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Auto Redirects'),
                        'name' => 'STPRETTYURLPRO_AUTO_REDIRECT',
                        'is_bool' => true,
                        'desc' => $this->l('Automatically create redirects for old URLs'),
                        'values' => array(
                            array(
                                'id' => 'auto_redirect_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ),
                            array(
                                'id' => 'auto_redirect_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            )
                        ),
                    ),
                    array(
                        'type' => 'select',
                        'label' => $this->l('Default Redirect Type'),
                        'name' => 'STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE',
                        'desc' => $this->l('Default HTTP redirect status code'),
                        'options' => array(
                            'query' => array(
                                array('id' => 301, 'name' => '301 - Permanent'),
                                array('id' => 302, 'name' => '302 - Temporary'),
                                array('id' => 303, 'name' => '303 - See Other'),
                            ),
                            'id' => 'id',
                            'name' => 'name'
                        ),
                    ),
                ),
                'submit' => array(
                    'title' => $this->l('Save'),
                ),
            ),
        );
    }

    /**
     * Set values for the inputs.
     */
    protected function getConfigFormValues()
    {
        return array(
            'STPRETTYURLPRO_ENABLED' => Configuration::get('STPRETTYURLPRO_ENABLED', true),
            'STPRETTYURLPRO_REMOVE_IDS' => Configuration::get('STPRETTYURLPRO_REMOVE_IDS', true),
            'STPRETTYURLPRO_AUTO_REDIRECT' => Configuration::get('STPRETTYURLPRO_AUTO_REDIRECT', true),
            'STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE' => Configuration::get('STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE', 301),
            'STPRETTYURLPRO_AUTO_META' => Configuration::get('STPRETTYURLPRO_AUTO_META', true),
            'STPRETTYURLPRO_SITEMAP_ENABLED' => Configuration::get('STPRETTYURLPRO_SITEMAP_ENABLED', true),
            'STPRETTYURLPRO_ROBOTS_ENABLED' => Configuration::get('STPRETTYURLPRO_ROBOTS_ENABLED', true),
            'STPRETTYURLPRO_OG_ENABLED' => Configuration::get('STPRETTYURLPRO_OG_ENABLED', true),
            'STPRETTYURLPRO_DUPLICATE_CHECK' => Configuration::get('STPRETTYURLPRO_DUPLICATE_CHECK', true),
            'STPRETTYURLPRO_CACHE_ENABLED' => Configuration::get('STPRETTYURLPRO_CACHE_ENABLED', true),
            'STPRETTYURLPRO_DEBUG_MODE' => Configuration::get('STPRETTYURLPRO_DEBUG_MODE', false),
        );
    }

    /**
     * Save form data.
     */
    protected function postProcess()
    {
        $form_values = $this->getConfigFormValues();

        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }

        $this->clearCache();
    }
}

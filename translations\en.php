<?php
/**
 * ST Pretty URL Pro English Translations
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

global $_MODULE;
$_MODULE = array();

// Module information
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_01'] = 'ST Pretty URL Pro';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_02'] = 'Advanced URL management, SEO optimization, and redirect management for PrestaShop';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_03'] = 'Are you sure you want to uninstall this module?';

// Configuration
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_04'] = 'Settings';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_05'] = 'Enable Module';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_06'] = 'Enable or disable the module functionality';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_07'] = 'Remove IDs from URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_08'] = 'Remove product and category IDs from URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_09'] = 'Auto Redirects';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_10'] = 'Automatically create redirects for old URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_11'] = 'Default Redirect Type';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_12'] = 'Default HTTP redirect status code';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_13'] = 'Auto Meta Tags';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_14'] = 'Automatically generate meta tags using templates';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_15'] = 'Sitemap Generation';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_16'] = 'Enable XML sitemap generation';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_17'] = 'Robots.txt Generation';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_18'] = 'Enable dynamic robots.txt generation';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_19'] = 'Open Graph Tags';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_20'] = 'Enable Open Graph meta tags generation';

// Status messages
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_21'] = 'Enabled';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_22'] = 'Disabled';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_23'] = 'Save';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_24'] = 'Settings updated successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_25'] = 'Advanced settings updated successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_26'] = 'Cache cleared successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_27'] = 'Sitemap generated successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_28'] = 'Failed to generate sitemap';

// Advanced settings
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_29'] = 'Advanced Settings';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_30'] = 'Enable Cache';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_31'] = 'Enable caching for better performance';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_32'] = 'Debug Mode';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_33'] = 'Enable debug mode for development';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_34'] = 'Duplicate URL Detection';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_35'] = 'Enable automatic duplicate URL detection';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_36'] = 'Twitter Site Handle';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_37'] = 'Your Twitter handle (e.g., @yoursite)';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_38'] = 'Custom Robots.txt Content';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_39'] = 'Additional content to add to robots.txt';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_40'] = 'Save Advanced Settings';

// Tools
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_41'] = 'Tools';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_42'] = 'Cache Management';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_43'] = 'Clear module cache to refresh URL mappings';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_44'] = 'Clear Cache';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_45'] = 'Generate and save sitemap.xml file';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_46'] = 'Generate Sitemap';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_47'] = 'Duplicate URL Scan';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_48'] = 'Scan for duplicate URLs across the site';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_49'] = 'Scan Duplicates';

// Redirect Manager
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_50'] = 'Redirect Manager';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_51'] = 'ID';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_52'] = 'Old URL';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_53'] = 'New URL';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_54'] = 'Type';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_55'] = 'Hits';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_56'] = 'Status';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_57'] = 'Date Added';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_58'] = 'Delete selected';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_59'] = 'Delete selected items?';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_60'] = 'Redirect';

// Redirect form
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_61'] = 'The old URL to redirect from (relative path)';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_62'] = 'The new URL to redirect to (can be relative or absolute)';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_63'] = 'Redirect Type';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_64'] = 'HTTP redirect status code';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_65'] = 'Active';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_66'] = 'Enable or disable this redirect';

// Bulk import
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_67'] = 'Bulk Import Redirects';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_68'] = 'Redirects';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_69'] = 'Enter one redirect per line in format: old_url|new_url|redirect_type (e.g., /old-page|/new-page|301)';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_70'] = 'Import Redirects';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_71'] = 'Please enter redirects to import';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_72'] = '%d redirects imported successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_73'] = '%d redirects failed to import';

// Duplicate URLs
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_74'] = 'Duplicate URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_75'] = 'URL';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_76'] = 'Object ID';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_77'] = 'Count';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_78'] = 'Pending';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_79'] = 'Resolved';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_80'] = 'Ignored';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_81'] = 'Mark as resolved';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_82'] = 'Mark as ignored';

// Scan form
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_83'] = 'Scan for Duplicate URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_84'] = 'Page Type';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_85'] = 'All Types';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_86'] = 'Products';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_87'] = 'Categories';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_88'] = 'CMS Pages';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_89'] = 'Select the type of pages to scan for duplicates';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_90'] = 'Start Scan';

// Statistics
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_91'] = 'Statistics';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_92'] = 'Total Duplicates';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_93'] = 'By Type';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_94'] = '%d duplicate URLs found';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_95'] = 'No duplicate URLs found';

// Meta Tags
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_96'] = 'Meta Tags Templates';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_97'] = 'Meta Title Template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_98'] = 'Meta Tag Template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_99'] = 'Product';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_100'] = 'Category';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_101'] = 'CMS';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_102'] = 'Manufacturer';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_103'] = 'Supplier';

// Variables help
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_104'] = 'Available Variables';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_105'] = 'You can use the following variables in your meta tag templates:';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_106'] = 'Common Variables';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_107'] = 'Page-Specific Variables';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_108'] = 'Variables';

// Template form
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_109'] = 'Select the page type for this template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_110'] = 'Template for meta title. Use variables like {product_name}, {category_name}, {shop_name}';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_111'] = 'Meta Description Template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_112'] = 'Template for meta description. Use variables like {product_description_short}, {category_description}';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_113'] = 'Meta Keywords Template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_114'] = 'Template for meta keywords. Use variables like {product_name}, {category_name}, {manufacturer_name}';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_115'] = 'H1 Tag Template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_116'] = 'Template for H1 tag. Use variables like {product_name}, {category_name}';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_117'] = 'Enable or disable this template';

// Error messages
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_118'] = 'Redirect deleted successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_119'] = 'Failed to delete redirect';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_120'] = 'Redirect not found';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_121'] = 'Template deleted successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_122'] = 'Failed to delete template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_123'] = 'Template not found';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_124'] = 'Status updated successfully';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_125'] = 'Failed to update status';

// Action buttons
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_126'] = 'Add new redirect';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_127'] = 'Add new template';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_128'] = 'Scan for duplicates';

// Welcome messages
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_129'] = 'Welcome to ST Pretty URL Pro!';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_130'] = 'This module provides advanced URL management, SEO optimization, and redirect management for your PrestaShop store.';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_131'] = 'URL Management';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_132'] = 'SEO Features';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_133'] = 'Redirect Management';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_134'] = 'Quality Control';

// Feature descriptions
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_135'] = 'Remove IDs from product and category URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_136'] = 'Custom URL patterns for different page types';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_137'] = 'Automatic redirect creation for old URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_138'] = 'Automatic meta tag generation with variables';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_139'] = 'XML sitemap generation';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_140'] = 'Dynamic robots.txt creation';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_141'] = 'Open Graph meta tags for social media';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_142'] = '301, 302, and 303 redirect support';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_143'] = 'Bulk import/export functionality';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_144'] = 'Hit tracking and analytics';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_145'] = 'Duplicate URL detection and reporting';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_146'] = 'URL validation and cleanup';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_147'] = 'Performance monitoring';

// Important notes
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_148'] = 'Important Notes';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_149'] = 'Always backup your database before making changes';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_150'] = 'Test URL changes on a staging environment first';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_151'] = 'Monitor your site for 404 errors after enabling URL cleaning';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_152'] = 'Clear your cache after configuration changes';

// Quick start guide
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_153'] = 'Quick Start Guide';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_154'] = 'Configure basic settings below';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_155'] = 'Set up URL patterns in the URL Patterns section';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_156'] = 'Configure meta tag templates for automatic SEO';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_157'] = 'Enable sitemap generation and submit to search engines';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_158'] = 'Monitor duplicate URLs and redirects regularly';

// Module statistics
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_159'] = 'Module Statistics';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_160'] = 'Active Redirects';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_161'] = 'Clean URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_162'] = 'Duplicate URLs';
$_MODULE['<{stprettyurlpro}prestashop>stprettyurlpro_163'] = 'Sitemap URLs';

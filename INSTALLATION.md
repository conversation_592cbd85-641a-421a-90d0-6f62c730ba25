# ST Pretty URL Pro - Installation Guide

## Pre-Installation Requirements

### System Requirements
- **PrestaShop**: 1.7.0 or higher (tested up to 9.x)
- **PHP**: 7.1 or higher (8.0+ recommended)
- **MySQL**: 5.6 or higher (8.0+ recommended)
- **Apache/Nginx**: with mod_rewrite enabled
- **Memory**: Minimum 64MB PHP memory limit
- **Disk Space**: At least 10MB free space

### PrestaShop Settings
- Friendly URLs must be enabled
- URL rewriting must be active
- Multistore compatibility (if using multistore)

## Installation Methods

### Method 1: Admin Panel Installation (Recommended)

1. **Download the Module**
   - Download `stprettyurlpro.zip` from your purchase location
   - Ensure the ZIP file is not corrupted

2. **Access Admin Panel**
   - Log in to your PrestaShop admin panel
   - Navigate to **Modules > Module Manager**

3. **Upload Module**
   - Click **"Upload a module"** button
   - Select the `stprettyurlpro.zip` file
   - Wait for upload to complete

4. **Install Module**
   - Find "ST Pretty URL Pro" in the module list
   - Click **"Install"** button
   - Wait for installation to complete

5. **Configure Module**
   - Click **"Configure"** after installation
   - Follow the configuration steps below

### Method 2: FTP Installation

1. **Extract Files**
   ```bash
   unzip stprettyurlpro.zip
   ```

2. **Upload via FTP**
   - Connect to your server via FTP
   - Navigate to `/modules/` directory
   - Upload the entire `stprettyurlpro` folder

3. **Set Permissions**
   ```bash
   chmod -R 755 modules/stprettyurlpro/
   chown -R www-data:www-data modules/stprettyurlpro/
   ```

4. **Install via Admin**
   - Go to **Modules > Module Manager**
   - Find "ST Pretty URL Pro"
   - Click **"Install"**

### Method 3: Command Line Installation

1. **Navigate to PrestaShop Root**
   ```bash
   cd /path/to/your/prestashop
   ```

2. **Extract Module**
   ```bash
   unzip stprettyurlpro.zip -d modules/
   ```

3. **Set Permissions**
   ```bash
   chmod -R 755 modules/stprettyurlpro/
   chown -R www-data:www-data modules/stprettyurlpro/
   ```

4. **Install via CLI** (PrestaShop 1.7.7+)
   ```bash
   php bin/console prestashop:module install stprettyurlpro
   ```

## Post-Installation Configuration

### Step 1: Basic Configuration

1. **Access Module Configuration**
   - Go to **Modules > ST Pretty URL Pro > Configuration**

2. **Enable Core Features**
   - ✅ Enable Module
   - ✅ Remove IDs from URLs
   - ✅ Auto Redirects
   - Set Default Redirect Type to **301**

3. **Enable SEO Features**
   - ✅ Auto Meta Tags
   - ✅ Sitemap Generation
   - ✅ Robots.txt Generation
   - ✅ Open Graph Tags

4. **Save Configuration**
   - Click **"Save"** to apply settings

### Step 2: Advanced Configuration

1. **Performance Settings**
   - ✅ Enable Cache (for better performance)
   - ❌ Debug Mode (only for development)
   - ✅ Duplicate URL Detection

2. **Social Media Settings**
   - Enter your Twitter handle (optional)
   - Configure custom robots.txt content (optional)

3. **Save Advanced Settings**

### Step 3: URL Patterns Configuration

1. **Navigate to URL Patterns**
   - Go to **ST Pretty URL Pro > URL Patterns**

2. **Review Default Patterns**
   - Product: `{category_rewrite}/{product_rewrite}.html`
   - Category: `{category_rewrite}/`
   - CMS: `content/{cms_rewrite}.html`

3. **Customize if Needed**
   - Modify patterns according to your SEO strategy
   - Test patterns on staging environment first

### Step 4: Meta Tag Templates

1. **Access Meta Tags Section**
   - Go to **ST Pretty URL Pro > Meta Tags**

2. **Review Default Templates**
   - Product template with variables
   - Category template with variables
   - CMS template with variables

3. **Customize Templates**
   - Use available variables for dynamic content
   - Test templates on different page types

### Step 5: Initial Scan and Setup

1. **Scan for Duplicates**
   - Go to **ST Pretty URL Pro > Duplicate URLs**
   - Click **"Start Scan"**
   - Review and resolve any duplicates found

2. **Generate Sitemap**
   - Go to **ST Pretty URL Pro > Configuration**
   - Click **"Generate Sitemap"** in Tools section
   - Verify sitemap at `/sitemap.xml`

3. **Test Redirects**
   - Create a test redirect
   - Verify it works correctly
   - Check redirect hit tracking

## Verification Steps

### 1. URL Cleaning Verification

Test URL cleaning by visiting:
- Old URL: `/123-product-name.html`
- Should redirect to: `/category/product-name.html`

### 2. Meta Tags Verification

1. **View Page Source**
   - Visit a product page
   - Check for auto-generated meta tags
   - Verify variable replacement

2. **SEO Tools**
   - Use tools like Google Search Console
   - Verify meta tags are properly set

### 3. Sitemap Verification

1. **Access Sitemap**
   - Visit `/sitemap.xml`
   - Verify XML structure is valid
   - Check URL count and format

2. **Submit to Search Engines**
   - Google Search Console
   - Bing Webmaster Tools

### 4. Robots.txt Verification

1. **Access Robots.txt**
   - Visit `/robots.txt`
   - Verify content is generated correctly
   - Check sitemap reference

## Troubleshooting Installation Issues

### Common Installation Problems

**Module Not Appearing**
```bash
# Check file permissions
ls -la modules/stprettyurlpro/
# Should show proper ownership and permissions
```

**Database Errors**
- Check MySQL user permissions
- Verify database connection
- Check error logs in `/var/log/`

**Memory Errors**
```php
// Increase PHP memory limit in php.ini
memory_limit = 128M
```

**Permission Errors**
```bash
# Fix permissions
chmod -R 755 modules/stprettyurlpro/
chown -R www-data:www-data modules/stprettyurlpro/
```

### Installation Verification

1. **Check Module Status**
   - Module appears in **Modules > Module Manager**
   - Status shows as "Installed"
   - Configuration link is available

2. **Check Database Tables**
   ```sql
   SHOW TABLES LIKE 'ps_st_pretty_url_%';
   ```
   Should show 8 tables created by the module

3. **Check File Structure**
   ```bash
   ls -la modules/stprettyurlpro/
   ```
   Verify all files and folders are present

## Multistore Installation

### For Multistore Environments

1. **Install on Main Store**
   - Install module on main store first
   - Configure basic settings

2. **Enable for Other Stores**
   - Go to **Modules > Module Manager**
   - Select store context
   - Enable module for each store

3. **Configure Per Store**
   - Each store can have different settings
   - URL patterns can vary per store
   - Meta templates can be store-specific

## Backup Recommendations

### Before Installation

1. **Database Backup**
   ```bash
   mysqldump -u username -p database_name > backup.sql
   ```

2. **File Backup**
   ```bash
   tar -czf prestashop_backup.tar.gz /path/to/prestashop/
   ```

3. **Configuration Backup**
   - Export current URL rewrite rules
   - Save current meta tag settings

### After Installation

1. **Test on Staging**
   - Always test on staging environment first
   - Verify all functionality works correctly
   - Check for conflicts with other modules

2. **Monitor for Issues**
   - Check error logs regularly
   - Monitor 404 errors
   - Verify redirect functionality

## Support and Next Steps

### Getting Help

1. **Documentation**
   - Read full documentation in `/docs/`
   - Check API reference for developers
   - Review examples and use cases

2. **Community Support**
   - PrestaShop community forums
   - GitHub issues for bug reports
   - Developer documentation

### Recommended Next Steps

1. **SEO Optimization**
   - Configure meta tag templates
   - Set up URL patterns
   - Generate and submit sitemap

2. **Performance Monitoring**
   - Enable caching
   - Monitor redirect performance
   - Regular duplicate URL scans

3. **Maintenance**
   - Regular backups
   - Monitor error logs
   - Keep module updated

## Uninstallation (if needed)

### Safe Uninstallation

1. **Backup Data**
   - Export redirect rules
   - Save meta tag templates
   - Backup configuration

2. **Uninstall Module**
   - Go to **Modules > Module Manager**
   - Find "ST Pretty URL Pro"
   - Click **"Uninstall"**

3. **Clean Up** (optional)
   - Remove module files via FTP
   - Clean up any remaining database entries

**Note**: Uninstalling will remove all module data including redirects and templates. Make sure to backup important data first.

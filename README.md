# ST Pretty URL Pro

**Advanced URL management, SEO optimization, and redirect management for PrestaShop 1.7-9**

## Overview

ST Pretty URL Pro is a comprehensive PrestaShop module that provides advanced URL management capabilities, SEO optimization tools, and redirect management features. This module helps improve your store's search engine visibility and user experience by creating clean, SEO-friendly URLs and managing redirects efficiently.

## Features

### 🔗 URL Management
- **Clean URLs**: Remove IDs from product, category, and CMS URLs
- **Custom URL Patterns**: Define custom URL structures for different page types
- **Automatic Redirects**: Create redirects automatically when URLs change
- **URL Validation**: Ensure URL consistency and prevent conflicts

### 🚀 SEO Optimization
- **Auto Meta Tags**: Generate meta tags automatically using customizable templates
- **Variable System**: Use dynamic variables in meta tag templates
- **XML Sitemap**: Generate comprehensive XML sitemaps
- **Robots.txt**: Dynamic robots.txt generation with custom rules
- **Open Graph**: Social media optimization with Open Graph meta tags

### 📊 Redirect Management
- **Multiple Redirect Types**: Support for 301, 302, and 303 redirects
- **Bulk Import/Export**: Manage redirects in bulk using CSV format
- **Hit Tracking**: Monitor redirect usage and performance
- **Advanced Filtering**: Find and manage redirects efficiently

### 🔍 Quality Control
- **Duplicate Detection**: Scan and report duplicate URLs
- **URL Monitoring**: Track URL changes and conflicts
- **Performance Analytics**: Monitor module performance impact
- **Debug Mode**: Development tools for troubleshooting

## Compatibility

- **PrestaShop**: 1.7.x - 9.x
- **PHP**: 7.1+ (8.0+ recommended)
- **MySQL**: 5.6+ (8.0+ recommended)

## Installation

### Method 1: Admin Panel Upload
1. Download the module ZIP file
2. Go to **Modules > Module Manager** in your PrestaShop admin
3. Click **Upload a module**
4. Select the ZIP file and upload
5. Click **Install** when the module appears

### Method 2: FTP Upload
1. Extract the ZIP file
2. Upload the `stprettyurlpro` folder to `/modules/` directory
3. Go to **Modules > Module Manager** in admin
4. Find "ST Pretty URL Pro" and click **Install**

### Method 3: Command Line
```bash
# Navigate to PrestaShop root
cd /path/to/prestashop

# Extract module
unzip stprettyurlpro.zip -d modules/

# Set permissions
chmod -R 755 modules/stprettyurlpro/
```

## Configuration

### Basic Setup
1. Go to **Modules > ST Pretty URL Pro > Configuration**
2. Enable the module functionality
3. Configure basic settings:
   - Enable URL cleaning
   - Set default redirect type
   - Enable auto meta tags
   - Enable sitemap generation

### Advanced Configuration
1. Navigate to **Advanced Settings** tab
2. Configure performance options:
   - Enable caching
   - Set debug mode (development only)
   - Configure duplicate URL detection

### URL Patterns
1. Go to **URL Patterns** section
2. Define custom URL structures for:
   - Products: `{category_rewrite}/{product_rewrite}.html`
   - Categories: `{category_rewrite}/`
   - CMS Pages: `content/{cms_rewrite}.html`

### Meta Tag Templates
1. Navigate to **Meta Tags** section
2. Create templates for different page types
3. Use variables like:
   - `{product_name}` - Product name
   - `{category_name}` - Category name
   - `{shop_name}` - Shop name
   - `{manufacturer_name}` - Manufacturer name

## Usage Examples

### Creating Clean URLs
```php
// Before: /123-my-product.html
// After:  /electronics/my-product.html

// Before: /2-home/45-smartphones
// After:  /smartphones/
```

### Meta Tag Templates
```html
<!-- Product Template -->
Title: {product_name} - {category_name} | {shop_name}
Description: {product_description_short} - Buy {product_name} at {shop_name}

<!-- Category Template -->
Title: {category_name} | {shop_name}
Description: Discover our {category_name} collection at {shop_name}
```

### Bulk Redirect Import
```
/old-product-url|/new-product-url|301
/discontinued-category|/new-category|301
/old-page.html|/new-page.html|302
```

## API Reference

### URL Cleaner
```php
$urlCleaner = StUrlCleaner::getInstance();
$cleanUrl = $urlCleaner->cleanProductUrl($productId, $attributeId, $langId);
```

### Redirect Manager
```php
$redirect = new StRedirectManager();
$redirect->old_url = '/old-url';
$redirect->new_url = '/new-url';
$redirect->redirect_type = 301;
$redirect->add();
```

### Meta Tag Generator
```php
$metaGenerator = new StMetaTagGenerator();
$metaTags = $metaGenerator->generateMetaTags();
```

## Troubleshooting

### Common Issues

**URLs not redirecting**
- Check if module is enabled
- Verify redirect rules in admin panel
- Clear PrestaShop cache
- Check .htaccess file permissions

**Meta tags not appearing**
- Enable auto meta tags in configuration
- Check template syntax
- Verify page type configuration
- Clear browser cache

**Sitemap not generating**
- Enable sitemap generation
- Check file permissions in root directory
- Verify URL rewrite rules
- Test sitemap URL: `/sitemap.xml`

### Debug Mode
Enable debug mode in advanced settings to:
- View URL processing information
- Monitor redirect execution
- Check meta tag generation
- Analyze performance impact

## Performance Optimization

### Caching
- Enable module caching for better performance
- Use Redis or Memcached if available
- Clear cache after configuration changes

### Database Optimization
- Regular cleanup of old redirects
- Monitor duplicate URL reports
- Optimize database tables periodically

## Security Considerations

- Always backup database before installation
- Test on staging environment first
- Monitor for 404 errors after URL changes
- Validate redirect targets to prevent open redirects

## Support

### Documentation
- Full documentation available in `/docs/` folder
- API reference in `/docs/api/`
- Examples in `/docs/examples/`

### Community
- GitHub Issues: Report bugs and feature requests
- PrestaShop Forums: Community discussions
- Developer Documentation: Technical guides

## Changelog

### Version 1.0.0
- Initial release
- URL cleaning functionality
- Redirect management
- Meta tag generation
- Sitemap generation
- Duplicate URL detection
- Open Graph support
- Multi-language support

## License

This module is licensed under the Academic Free License (AFL 3.0).

## Credits

Developed by ST-themes
Copyright © 2024 ST-themes

## Requirements

- PrestaShop 1.7.0 or higher
- PHP 7.1 or higher
- MySQL 5.6 or higher
- mod_rewrite enabled
- Minimum 64MB PHP memory limit

## File Structure

```
stprettyurlpro/
├── stprettyurlpro.php          # Main module file
├── config.xml                  # Module configuration
├── README.md                   # This file
├── classes/                    # Module classes
│   ├── StPrettyUrlConfig.php
│   ├── StUrlCleaner.php
│   ├── StRedirectManager.php
│   ├── StMetaTagGenerator.php
│   ├── StSitemapGenerator.php
│   ├── StDuplicateUrlDetector.php
│   └── StOpenGraphManager.php
├── controllers/                # Controllers
│   ├── admin/                  # Admin controllers
│   └── front/                  # Front controllers
├── sql/                        # Database scripts
│   ├── install.php
│   └── uninstall.php
├── views/                      # Templates and assets
│   ├── templates/
│   ├── css/
│   └── js/
├── translations/               # Language files
└── ajax/                      # AJAX endpoints
```

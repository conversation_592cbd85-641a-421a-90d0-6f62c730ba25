<?php
/**
 * ST Pretty URL Pro Robots Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StPrettyUrlProRobotsModuleFrontController extends ModuleFrontController
{
    public $ssl = true;

    /**
     * Initialize controller
     */
    public function init()
    {
        parent::init();
        
        // Check if robots.txt is enabled
        if (!Configuration::get('STPRETTYURLPRO_ROBOTS_ENABLED')) {
            Tools::redirect('index.php?controller=404');
        }
    }

    /**
     * Process robots.txt request
     */
    public function initContent()
    {
        // Set text content type
        header('Content-Type: text/plain; charset=utf-8');
        
        // Generate robots.txt
        $sitemap_generator = new StSitemapGenerator();
        $robots_txt = $sitemap_generator->generateRobotsTxt();
        
        // Output robots.txt
        echo $robots_txt;
        exit;
    }

    /**
     * Set media (disable CSS/JS for text output)
     */
    public function setMedia()
    {
        // Don't load any CSS or JS for text output
        return;
    }

    /**
     * Display template (not used for text output)
     */
    public function display()
    {
        // Text is output directly in initContent
        return;
    }
}

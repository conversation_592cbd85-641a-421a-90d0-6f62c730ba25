<?php
/**
 * ST Pretty URL Pro Redirect Manager Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStRedirectManagerController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'st_pretty_url_redirects';
        $this->className = 'StRedirectManager';
		$this->identifier = 'id_redirect';
		$this->list_id = $this->table;
        $this->lang = false;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->context = Context::getContext();


		parent::__construct();
        $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->module->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->module->l('Delete selected items?')
            )
        );

        $this->fields_list = array(
            'id_redirect' => array(
                'title' => $this->module->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'old_url' => array(
                'title' => $this->module->l('Old URL'),
                'width' => 'auto'
            ),
            'new_url' => array(
                'title' => $this->module->l('New URL'),
                'width' => 'auto'
            ),
            'redirect_type' => array(
                'title' => $this->module->l('Type'),
                'align' => 'center',
                'class' => 'fixed-width-xs',
                'type' => 'select',
                'list' => array(
                    301 => '301',
                    302 => '302',
                    303 => '303'
                ),
                'filter_key' => 'a!redirect_type'
            ),
            'hits' => array(
                'title' => $this->module->l('Hits'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'active' => array(
                'title' => $this->module->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'date_add' => array(
                'title' => $this->module->l('Date Added'),
                'align' => 'center',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            )
        );

       

        $this->meta_title = $this->module->l('Redirect Manager');
    }

    /**
     * Render form
     */
    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->module->l('Redirect'),
                'icon' => 'icon-share'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->module->l('Old URL'),
                    'name' => 'old_url',
                    'size' => 60,
                    'required' => true,
                    'desc' => $this->module->l('The old URL to redirect from (relative path)')
                ),
                array(
                    'type' => 'text',
                    'label' => $this->module->l('New URL'),
                    'name' => 'new_url',
                    'size' => 60,
                    'required' => true,
                    'desc' => $this->module->l('The new URL to redirect to (can be relative or absolute)')
                ),
                array(
                    'type' => 'select',
                    'label' => $this->module->l('Redirect Type'),
                    'name' => 'redirect_type',
                    'required' => true,
                    'options' => array(
                        'query' => array(
                            array('id' => 301, 'name' => '301 - Permanent'),
                            array('id' => 302, 'name' => '302 - Temporary'),
                            array('id' => 303, 'name' => '303 - See Other'),
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->module->l('HTTP redirect status code')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->module->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->module->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->module->l('Disabled')
                        )
                    ),
                    'desc' => $this->module->l('Enable or disable this redirect')
                )
            ),
            'submit' => array(
                'title' => $this->module->l('Save'),
            )
        );

        if (!($obj = $this->module->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        if ($this->display == 'add' || $this->display == 'edit') {
            $this->content .= $this->renderForm();
        } else {
            $this->content .= $this->renderList();
            $this->content .= $this->renderBulkImportForm();
        }

        $this->context->smarty->assign(array(
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ));
    }

    /**
     * Render bulk import form
     */
    public function renderBulkImportForm()
    {
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'bulk_import';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = 'id_bulk_import';
        $helper->submit_action = 'submitBulkImport';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminStRedirectManager', false);
        $helper->token = Tools::getAdminTokenLite('AdminStRedirectManager');

        $helper->tpl_vars = array(
            'fields_value' => array(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        $form = array(
            'form' => array(
                'legend' => array(
                    'title' => $this->module->l('Bulk Import Redirects'),
                    'icon' => 'icon-upload',
                ),
                'input' => array(
                    array(
                        'type' => 'textarea',
                        'label' => $this->module->l('Redirects'),
                        'name' => 'bulk_redirects',
                        'rows' => 10,
                        'cols' => 60,
                        'desc' => $this->module->l('Enter one redirect per line in format: old_url|new_url|redirect_type (e.g., /old-page|/new-page|301)')
                    ),
                ),
                'submit' => array(
                    'title' => $this->module->l('Import Redirects'),
                ),
            ),
        );

        return $helper->generateForm(array($form));
    }

    /**
     * Process bulk import
     */
    public function processBulkImport()
    {
        $bulk_redirects = Tools::getValue('bulk_redirects');
        if (empty($bulk_redirects)) {
            $this->errors[] = $this->module->l('Please enter redirects to import');
            return;
        }

        $lines = explode("\n", $bulk_redirects);
        $imported = 0;
        $errors = 0;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            $parts = explode('|', $line);
            if (count($parts) < 2) {
                $errors++;
                continue;
            }

            $old_url = trim($parts[0]);
            $new_url = trim($parts[1]);
            $redirect_type = isset($parts[2]) ? (int)trim($parts[2]) : 301;

            if (!in_array($redirect_type, array(301, 302, 303))) {
                $redirect_type = 301;
            }

            $redirect = new StRedirectManager();
            $redirect->old_url = $old_url;
            $redirect->new_url = $new_url;
            $redirect->redirect_type = $redirect_type;
            $redirect->active = 1;
            $redirect->hits = 0;

            if ($redirect->add()) {
                $imported++;
            } else {
                $errors++;
            }
        }

        if ($imported > 0) {
            $this->confirmations[] = sprintf($this->module->l('%d redirects imported successfully'), $imported);
        }

        if ($errors > 0) {
            $this->errors[] = sprintf($this->module->l('%d redirects failed to import'), $errors);
        }
    }

    /**
     * Post process
     */
    public function postProcess()
    {
        if (Tools::isSubmit('submitBulkImport')) {
            $this->processBulkImport();
        }

        return parent::postProcess();
    }

    /**
     * Initialize page header toolbar
     */
    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_redirect'] = array(
                'href' => self::$currentIndex . '&addst_pretty_url_redirects&token=' . $this->token,
                'desc' => $this->module->l('Add new redirect', null, null, false),
                'icon' => 'process-icon-new'
            );
        }

        parent::initPageHeaderToolbar();
    }

    /**
     * Process delete
     */
    public function processDelete()
    {
        if (Validate::isLoadedObject($object = $this->module->loadObject())) {
            if ($object->delete()) {
                $this->confirmations[] = $this->module->l('Redirect deleted successfully');
            } else {
                $this->errors[] = $this->module->l('Failed to delete redirect');
            }
        } else {
            $this->errors[] = $this->module->l('Redirect not found');
        }
    }

    /**
     * Process bulk delete
     */
    public function processBulkDelete()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $deleted = StRedirectManager::bulkDeleteRedirects($this->boxes);
            if ($deleted) {
                $this->confirmations[] = sprintf($this->module->l('%d redirects deleted successfully'), count($this->boxes));
            } else {
                $this->errors[] = $this->module->l('Failed to delete redirects');
            }
        }
    }
}

<?php
/**
 * ST Pretty URL Pro Admin Configuration Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStPrettyUrlProConfigController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        $this->lang = false;
        
        parent::__construct();
        
        $this->meta_title = $this->module->l('ST Pretty URL Pro Configuration');
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        parent::initContent();
        
        $this->content .= $this->renderConfigurationForm();
        $this->content .= $this->renderAdvancedConfigurationForm();
        $this->content .= $this->renderToolsSection();
    }

    /**
     * Render main configuration form
     */
    public function renderConfigurationForm()
    {
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'configuration';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = 'id_configuration';
        $helper->submit_action = 'submitStPrettyUrlProConfig';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminStPrettyUrlProConfig', false);
        $helper->token = Tools::getAdminTokenLite('AdminStPrettyUrlProConfig');

        $helper->tpl_vars = array(
            'fields_value' => $this->getConfigFormValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        return $helper->generateForm(array($this->getConfigForm()));
    }

    /**
     * Get main configuration form structure
     */
    protected function getConfigForm()
    {
        return array(
            'form' => array(
                'legend' => array(
                    'title' => $this->module->l('General Settings'),
                    'icon' => 'icon-cogs',
                ),
                'input' => array(
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Enable Module'),
                        'name' => 'STPRETTYURLPRO_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable or disable the module functionality'),
                        'values' => array(
                            array('id' => 'active_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'active_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Remove IDs from URLs'),
                        'name' => 'STPRETTYURLPRO_REMOVE_IDS',
                        'is_bool' => true,
                        'desc' => $this->module->l('Remove product and category IDs from URLs'),
                        'values' => array(
                            array('id' => 'remove_ids_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'remove_ids_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Auto Redirects'),
                        'name' => 'STPRETTYURLPRO_AUTO_REDIRECT',
                        'is_bool' => true,
                        'desc' => $this->module->l('Automatically create redirects for old URLs'),
                        'values' => array(
                            array('id' => 'auto_redirect_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'auto_redirect_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'select',
                        'label' => $this->module->l('Default Redirect Type'),
                        'name' => 'STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE',
                        'desc' => $this->module->l('Default HTTP redirect status code'),
                        'options' => array(
                            'query' => array(
                                array('id' => 301, 'name' => '301 - Permanent'),
                                array('id' => 302, 'name' => '302 - Temporary'),
                                array('id' => 303, 'name' => '303 - See Other'),
                            ),
                            'id' => 'id',
                            'name' => 'name'
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Auto Meta Tags'),
                        'name' => 'STPRETTYURLPRO_AUTO_META',
                        'is_bool' => true,
                        'desc' => $this->module->l('Automatically generate meta tags using templates'),
                        'values' => array(
                            array('id' => 'auto_meta_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'auto_meta_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Sitemap Generation'),
                        'name' => 'STPRETTYURLPRO_SITEMAP_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable XML sitemap generation'),
                        'values' => array(
                            array('id' => 'sitemap_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'sitemap_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Robots.txt Generation'),
                        'name' => 'STPRETTYURLPRO_ROBOTS_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable dynamic robots.txt generation'),
                        'values' => array(
                            array('id' => 'robots_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'robots_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Open Graph Tags'),
                        'name' => 'STPRETTYURLPRO_OG_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable Open Graph meta tags generation'),
                        'values' => array(
                            array('id' => 'og_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'og_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                ),
                'submit' => array(
                    'title' => $this->module->l('Save'),
                ),
            ),
        );
    }

    /**
     * Render advanced configuration form
     */
    public function renderAdvancedConfigurationForm()
    {
        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = 'configuration';
        $helper->module = $this->module;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = 'id_configuration';
        $helper->submit_action = 'submitStPrettyUrlProAdvanced';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminStPrettyUrlProConfig', false);
        $helper->token = Tools::getAdminTokenLite('AdminStPrettyUrlProConfig');

        $helper->tpl_vars = array(
            'fields_value' => $this->getAdvancedConfigFormValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        return $helper->generateForm(array($this->getAdvancedConfigForm()));
    }

    /**
     * Get advanced configuration form structure
     */
    protected function getAdvancedConfigForm()
    {
        return array(
            'form' => array(
                'legend' => array(
                    'title' => $this->module->l('Advanced Settings'),
                    'icon' => 'icon-cog',
                ),
                'input' => array(
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Enable Cache'),
                        'name' => 'STPRETTYURLPRO_CACHE_ENABLED',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable caching for better performance'),
                        'values' => array(
                            array('id' => 'cache_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'cache_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Debug Mode'),
                        'name' => 'STPRETTYURLPRO_DEBUG_MODE',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable debug mode for development'),
                        'values' => array(
                            array('id' => 'debug_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'debug_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->module->l('Duplicate URL Detection'),
                        'name' => 'STPRETTYURLPRO_DUPLICATE_CHECK',
                        'is_bool' => true,
                        'desc' => $this->module->l('Enable automatic duplicate URL detection'),
                        'values' => array(
                            array('id' => 'duplicate_on', 'value' => true, 'label' => $this->module->l('Enabled')),
                            array('id' => 'duplicate_off', 'value' => false, 'label' => $this->module->l('Disabled'))
                        ),
                    ),
                    array(
                        'type' => 'text',
                        'label' => $this->module->l('Twitter Site Handle'),
                        'name' => 'STPRETTYURLPRO_TWITTER_SITE',
                        'desc' => $this->module->l('Your Twitter handle (e.g., @yoursite)'),
                        'size' => 50,
                    ),
                    array(
                        'type' => 'textarea',
                        'label' => $this->module->l('Custom Robots.txt Content'),
                        'name' => 'STPRETTYURLPRO_ROBOTS_CUSTOM',
                        'desc' => $this->module->l('Additional content to add to robots.txt'),
                        'rows' => 5,
                        'cols' => 60,
                    ),
                ),
                'submit' => array(
                    'title' => $this->module->l('Save Advanced Settings'),
                ),
            ),
        );
    }

    /**
     * Render tools section
     */
    public function renderToolsSection()
    {
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-wrench"></i> ' . $this->module->l('Tools') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-4">';
        $html .= '<h4>' . $this->module->l('Cache Management') . '</h4>';
        $html .= '<p>' . $this->module->l('Clear module cache to refresh URL mappings') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStPrettyUrlProConfig') . '&action=clearCache&token=' . Tools::getAdminTokenLite('AdminStPrettyUrlProConfig') . '" class="btn btn-warning">' . $this->module->l('Clear Cache') . '</a>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-4">';
        $html .= '<h4>' . $this->module->l('Sitemap Generation') . '</h4>';
        $html .= '<p>' . $this->module->l('Generate and save sitemap.xml file') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStPrettyUrlProConfig') . '&action=generateSitemap&token=' . Tools::getAdminTokenLite('AdminStPrettyUrlProConfig') . '" class="btn btn-primary">' . $this->module->l('Generate Sitemap') . '</a>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-4">';
        $html .= '<h4>' . $this->module->l('Duplicate URL Scan') . '</h4>';
        $html .= '<p>' . $this->module->l('Scan for duplicate URLs across the site') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStDuplicateUrls') . '" class="btn btn-info">' . $this->module->l('Scan Duplicates') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Get configuration form values
     */
    protected function getConfigFormValues()
    {
        return array(
            'STPRETTYURLPRO_ENABLED' => Configuration::get('STPRETTYURLPRO_ENABLED', true),
            'STPRETTYURLPRO_REMOVE_IDS' => Configuration::get('STPRETTYURLPRO_REMOVE_IDS', true),
            'STPRETTYURLPRO_AUTO_REDIRECT' => Configuration::get('STPRETTYURLPRO_AUTO_REDIRECT', true),
            'STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE' => Configuration::get('STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE', 301),
            'STPRETTYURLPRO_AUTO_META' => Configuration::get('STPRETTYURLPRO_AUTO_META', true),
            'STPRETTYURLPRO_SITEMAP_ENABLED' => Configuration::get('STPRETTYURLPRO_SITEMAP_ENABLED', true),
            'STPRETTYURLPRO_ROBOTS_ENABLED' => Configuration::get('STPRETTYURLPRO_ROBOTS_ENABLED', true),
            'STPRETTYURLPRO_OG_ENABLED' => Configuration::get('STPRETTYURLPRO_OG_ENABLED', true),
        );
    }

    /**
     * Get advanced configuration form values
     */
    protected function getAdvancedConfigFormValues()
    {
        return array(
            'STPRETTYURLPRO_CACHE_ENABLED' => Configuration::get('STPRETTYURLPRO_CACHE_ENABLED', true),
            'STPRETTYURLPRO_DEBUG_MODE' => Configuration::get('STPRETTYURLPRO_DEBUG_MODE', false),
            'STPRETTYURLPRO_DUPLICATE_CHECK' => Configuration::get('STPRETTYURLPRO_DUPLICATE_CHECK', true),
            'STPRETTYURLPRO_TWITTER_SITE' => Configuration::get('STPRETTYURLPRO_TWITTER_SITE', ''),
            'STPRETTYURLPRO_ROBOTS_CUSTOM' => Configuration::get('STPRETTYURLPRO_ROBOTS_CUSTOM', ''),
        );
    }

    /**
     * Process form submission
     */
    public function postProcess()
    {
        if (Tools::isSubmit('submitStPrettyUrlProConfig')) {
            $this->processConfigurationForm();
        } elseif (Tools::isSubmit('submitStPrettyUrlProAdvanced')) {
            $this->processAdvancedConfigurationForm();
        } elseif (Tools::getValue('action') === 'clearCache') {
            $this->processClearCache();
        } elseif (Tools::getValue('action') === 'generateSitemap') {
            $this->processGenerateSitemap();
        }
    }

    /**
     * Process main configuration form
     */
    protected function processConfigurationForm()
    {
        $form_values = $this->getConfigFormValues();
        
        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
        
        $this->confirmations[] = $this->module->l('Settings updated successfully');
    }

    /**
     * Process advanced configuration form
     */
    protected function processAdvancedConfigurationForm()
    {
        $form_values = $this->getAdvancedConfigFormValues();
        
        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
        
        $this->confirmations[] = $this->module->l('Advanced settings updated successfully');
    }

    /**
     * Process clear cache action
     */
    protected function processClearCache()
    {
        Cache::clean('stprettyurlpro_*');
        $this->confirmations[] = $this->module->l('Cache cleared successfully');
    }

    /**
     * Process generate sitemap action
     */
    protected function processGenerateSitemap()
    {
        $sitemap_generator = new StSitemapGenerator();
        if ($sitemap_generator->saveSitemapToFile()) {
            $this->confirmations[] = $this->module->l('Sitemap generated successfully');
        } else {
            $this->errors[] = $this->module->l('Failed to generate sitemap');
        }
    }
}

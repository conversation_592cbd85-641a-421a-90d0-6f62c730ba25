<?php
/**
 * ST Pretty URL Pro Module - Install SQL
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

$sql = array();

// Table for redirects management
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_redirects` (
    `id_redirect` int(11) NOT NULL AUTO_INCREMENT,
    `old_url` varchar(255) NOT NULL,
    `new_url` varchar(255) NOT NULL,
    `redirect_type` int(3) NOT NULL DEFAULT 301,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `hits` int(11) NOT NULL DEFAULT 0,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_redirect`),
    <PERSON><PERSON>Y `old_url` (`old_url`),
    <PERSON><PERSON><PERSON> `active` (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for meta tags templates
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_meta_templates` (
    `id_meta_template` int(11) NOT NULL AUTO_INCREMENT,
    `page_type` varchar(50) NOT NULL,
    `meta_title` text,
    `meta_description` text,
    `meta_keywords` text,
    `h1_tag` text,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_meta_template`),
    KEY `page_type` (`page_type`),
    KEY `active` (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for meta tags templates lang
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_meta_templates_lang` (
    `id_meta_template` int(11) NOT NULL,
    `id_lang` int(11) NOT NULL,
    `meta_title` text,
    `meta_description` text,
    `meta_keywords` text,
    `h1_tag` text,
    PRIMARY KEY (`id_meta_template`, `id_lang`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for Open Graph configurations
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_opengraph` (
    `id_opengraph` int(11) NOT NULL AUTO_INCREMENT,
    `page_type` varchar(50) NOT NULL,
    `og_title` text,
    `og_description` text,
    `og_image` varchar(255),
    `og_type` varchar(50) DEFAULT "website",
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_opengraph`),
    KEY `page_type` (`page_type`),
    KEY `active` (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for Open Graph configurations lang
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_opengraph_lang` (
    `id_opengraph` int(11) NOT NULL,
    `id_lang` int(11) NOT NULL,
    `og_title` text,
    `og_description` text,
    PRIMARY KEY (`id_opengraph`, `id_lang`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for duplicate URLs detection
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_duplicates` (
    `id_duplicate` int(11) NOT NULL AUTO_INCREMENT,
    `url` varchar(255) NOT NULL,
    `page_type` varchar(50) NOT NULL,
    `object_id` int(11) NOT NULL,
    `id_lang` int(11) NOT NULL,
    `id_shop` int(11) NOT NULL,
    `duplicate_count` int(11) NOT NULL DEFAULT 1,
    `status` enum("pending", "resolved", "ignored") DEFAULT "pending",
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_duplicate`),
    KEY `url` (`url`),
    KEY `page_type` (`page_type`),
    KEY `status` (`status`),
    KEY `id_shop` (`id_shop`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for advanced configurations
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_config` (
    `id_config` int(11) NOT NULL AUTO_INCREMENT,
    `config_key` varchar(100) NOT NULL,
    `config_value` text,
    `config_type` varchar(20) DEFAULT "string",
    `id_shop` int(11) NOT NULL DEFAULT 0,
    `id_shop_group` int(11) NOT NULL DEFAULT 0,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_config`),
    UNIQUE KEY `config_key_shop` (`config_key`, `id_shop`, `id_shop_group`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for URL patterns
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_patterns` (
    `id_pattern` int(11) NOT NULL AUTO_INCREMENT,
    `pattern_name` varchar(100) NOT NULL,
    `page_type` varchar(50) NOT NULL,
    `url_pattern` varchar(255) NOT NULL,
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `priority` int(11) NOT NULL DEFAULT 0,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_pattern`),
    KEY `page_type` (`page_type`),
    KEY `active` (`active`),
    KEY `priority` (`priority`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

// Table for sitemap configuration
$sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_sitemap` (
    `id_sitemap` int(11) NOT NULL AUTO_INCREMENT,
    `page_type` varchar(50) NOT NULL,
    `include_in_sitemap` tinyint(1) NOT NULL DEFAULT 1,
    `priority` decimal(2,1) NOT NULL DEFAULT 0.5,
    `changefreq` varchar(20) DEFAULT "weekly",
    `active` tinyint(1) NOT NULL DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_sitemap`),
    KEY `page_type` (`page_type`),
    KEY `active` (`active`)
) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}

// Insert default meta templates
$default_meta_templates = array(
    array(
        'page_type' => 'product',
        'meta_title' => '{product_name} - {category_name} | {shop_name}',
        'meta_description' => '{product_description_short} - Buy {product_name} at {shop_name}',
        'meta_keywords' => '{product_name}, {category_name}, {manufacturer_name}',
        'h1_tag' => '{product_name}'
    ),
    array(
        'page_type' => 'category',
        'meta_title' => '{category_name} | {shop_name}',
        'meta_description' => 'Discover our {category_name} collection at {shop_name}',
        'meta_keywords' => '{category_name}, {shop_name}',
        'h1_tag' => '{category_name}'
    ),
    array(
        'page_type' => 'cms',
        'meta_title' => '{cms_title} | {shop_name}',
        'meta_description' => '{cms_meta_description}',
        'meta_keywords' => '{cms_meta_keywords}',
        'h1_tag' => '{cms_title}'
    )
);

foreach ($default_meta_templates as $template) {
    Db::getInstance()->insert('st_pretty_url_meta_templates', array(
        'page_type' => pSQL($template['page_type']),
        'meta_title' => pSQL($template['meta_title']),
        'meta_description' => pSQL($template['meta_description']),
        'meta_keywords' => pSQL($template['meta_keywords']),
        'h1_tag' => pSQL($template['h1_tag']),
        'active' => 1,
        'date_add' => date('Y-m-d H:i:s'),
        'date_upd' => date('Y-m-d H:i:s')
    ));
}

// Insert default URL patterns
$default_patterns = array(
    array(
        'pattern_name' => 'Product Clean URL',
        'page_type' => 'product',
        'url_pattern' => '{category_rewrite}/{product_rewrite}.html',
        'priority' => 1
    ),
    array(
        'pattern_name' => 'Category Clean URL',
        'page_type' => 'category',
        'url_pattern' => '{category_rewrite}/',
        'priority' => 1
    ),
    array(
        'pattern_name' => 'CMS Clean URL',
        'page_type' => 'cms',
        'url_pattern' => 'content/{cms_rewrite}.html',
        'priority' => 1
    )
);

foreach ($default_patterns as $pattern) {
    Db::getInstance()->insert('st_pretty_url_patterns', array(
        'pattern_name' => pSQL($pattern['pattern_name']),
        'page_type' => pSQL($pattern['page_type']),
        'url_pattern' => pSQL($pattern['url_pattern']),
        'active' => 1,
        'priority' => (int)$pattern['priority'],
        'date_add' => date('Y-m-d H:i:s'),
        'date_upd' => date('Y-m-d H:i:s')
    ));
}

// Insert default sitemap configuration
$default_sitemap_config = array(
    array('page_type' => 'product', 'priority' => 0.8, 'changefreq' => 'weekly'),
    array('page_type' => 'category', 'priority' => 0.6, 'changefreq' => 'weekly'),
    array('page_type' => 'cms', 'priority' => 0.4, 'changefreq' => 'monthly'),
    array('page_type' => 'manufacturer', 'priority' => 0.5, 'changefreq' => 'monthly'),
    array('page_type' => 'supplier', 'priority' => 0.5, 'changefreq' => 'monthly')
);

foreach ($default_sitemap_config as $config) {
    Db::getInstance()->insert('st_pretty_url_sitemap', array(
        'page_type' => pSQL($config['page_type']),
        'include_in_sitemap' => 1,
        'priority' => (float)$config['priority'],
        'changefreq' => pSQL($config['changefreq']),
        'active' => 1,
        'date_add' => date('Y-m-d H:i:s'),
        'date_upd' => date('Y-m-d H:i:s')
    ));
}

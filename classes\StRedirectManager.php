<?php
/**
 * ST Pretty URL Pro Redirect Manager Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StRedirectManager extends ObjectModel
{
    public $id_redirect;
    public $old_url;
    public $new_url;
    public $redirect_type;
    public $active;
    public $hits;
    public $date_add;
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'st_pretty_url_redirects',
        'primary' => 'id_redirect',
        'fields' => array(
            'old_url' => array('type' => self::TYPE_STRING, 'validate' => 'isUrl', 'required' => true, 'size' => 255),
            'new_url' => array('type' => self::TYPE_STRING, 'validate' => 'isUrl', 'required' => true, 'size' => 255),
            'redirect_type' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true),
            'active' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool'),
            'hits' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'),
            'date_add' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
            'date_upd' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
        ),
    );

    /**
     * Handle redirects for current request
     */
    public function handleRedirects()
    {
        if (!StPrettyUrlConfig::isEnabled()) {
            return;
        }

        $current_url = $this->getCurrentUrl();
        $redirect = $this->findRedirect($current_url);

        if ($redirect) {
            $this->incrementHits($redirect['id_redirect']);
            $this->performRedirect($redirect['new_url'], $redirect['redirect_type']);
        }

        // Check for automatic redirects (old URLs with IDs)
        if (StPrettyUrlConfig::isAutoRedirectEnabled()) {
            $this->handleAutoRedirect($current_url);
        }
    }

    /**
     * Get current URL
     */
    private function getCurrentUrl()
    {
        $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];
        
        return $protocol . $host . $uri;
    }

    /**
     * Find redirect for URL
     */
    private function findRedirect($url)
    {
        // Normalize URL for comparison
        $normalized_url = $this->normalizeUrl($url);
        
        $sql = 'SELECT * FROM ' . _DB_PREFIX_ . 'st_pretty_url_redirects 
                WHERE active = 1 
                AND (old_url = "' . pSQL($url) . '" OR old_url = "' . pSQL($normalized_url) . '")
                ORDER BY id_redirect DESC 
                ';

        return Db::getInstance()->getRow($sql);
    }

    /**
     * Normalize URL for comparison
     */
    private function normalizeUrl($url)
    {
        // Remove protocol and domain
        $parsed = parse_url($url);
        $path = isset($parsed['path']) ? $parsed['path'] : '/';
        $query = isset($parsed['query']) ? '?' . $parsed['query'] : '';
        
        return $path . $query;
    }

    /**
     * Perform redirect
     */
    private function performRedirect($new_url, $redirect_type = 301)
    {
        $redirect_codes = array(
            301 => 'Moved Permanently',
            302 => 'Found',
            303 => 'See Other'
        );

        if (!isset($redirect_codes[$redirect_type])) {
            $redirect_type = 301;
        }

        header('HTTP/1.1 ' . $redirect_type . ' ' . $redirect_codes[$redirect_type]);
        header('Location: ' . $new_url);
        exit;
    }

    /**
     * Handle automatic redirects for old URLs
     */
    private function handleAutoRedirect($current_url)
    {
        $url_cleaner = StUrlCleaner::getInstance();
        
        if (!$url_cleaner->needsCleaning($current_url)) {
            return;
        }

        $clean_url = $this->generateCleanUrlFromOld($current_url);
        
        if ($clean_url && $clean_url !== $current_url) {
            // Create automatic redirect
            $this->createAutoRedirect($current_url, $clean_url);
            $this->performRedirect($clean_url, StPrettyUrlConfig::getDefaultRedirectType());
        }
    }

    /**
     * Generate clean URL from old URL
     */
    private function generateCleanUrlFromOld($old_url)
    {
        $parsed = parse_url($old_url);
        $path = isset($parsed['path']) ? $parsed['path'] : '';
        $query = isset($parsed['query']) ? $parsed['query'] : '';
        
        parse_str($query, $params);
        
        $url_cleaner = StUrlCleaner::getInstance();
        
        // Check for product URL
        if (isset($params['id_product'])) {
            $id_product = (int)$params['id_product'];
            $id_product_attribute = isset($params['id_product_attribute']) ? (int)$params['id_product_attribute'] : null;
            $clean_url = $url_cleaner->cleanProductUrl($id_product, $id_product_attribute);
            
            if ($clean_url) {
                return Context::getContext()->shop->getBaseURL() . $clean_url;
            }
        }
        
        // Check for category URL
        if (isset($params['id_category'])) {
            $id_category = (int)$params['id_category'];
            $clean_url = $url_cleaner->cleanCategoryUrl($id_category);
            
            if ($clean_url) {
                return Context::getContext()->shop->getBaseURL() . $clean_url;
            }
        }
        
        // Check for CMS URL
        if (isset($params['id_cms'])) {
            $id_cms = (int)$params['id_cms'];
            $clean_url = $url_cleaner->cleanCmsUrl($id_cms);
            
            if ($clean_url) {
                return Context::getContext()->shop->getBaseURL() . $clean_url;
            }
        }
        
        // Check for URL with ID in path (e.g., /123-product-name)
        if (preg_match('/\/(\d+)-([^\/]+)/', $path, $matches)) {
            $id = (int)$matches[1];
            $rewrite = $matches[2];
            
            // Try to determine object type and generate clean URL
            $clean_url = $this->guessCleanUrlFromPath($id, $rewrite, $path);
            if ($clean_url) {
                return Context::getContext()->shop->getBaseURL() . $clean_url;
            }
        }
        
        return null;
    }

    /**
     * Guess clean URL from path
     */
    private function guessCleanUrlFromPath($id, $rewrite, $path)
    {
        $url_cleaner = StUrlCleaner::getInstance();
        
        // Check if it's a product
        $product = new Product($id, false, Context::getContext()->language->id);
        if (Validate::isLoadedObject($product) && $product->link_rewrite === $rewrite) {
            return $url_cleaner->cleanProductUrl($id);
        }
        
        // Check if it's a category
        $category = new Category($id, Context::getContext()->language->id);
        if (Validate::isLoadedObject($category) && $category->link_rewrite === $rewrite) {
            return $url_cleaner->cleanCategoryUrl($id);
        }
        
        // Check if it's a CMS
        $cms = new CMS($id, Context::getContext()->language->id);
        if (Validate::isLoadedObject($cms) && $cms->link_rewrite === $rewrite) {
            return $url_cleaner->cleanCmsUrl($id);
        }
        
        return null;
    }

    /**
     * Create automatic redirect
     */
    private function createAutoRedirect($old_url, $new_url)
    {
        // Check if redirect already exists
        $existing = $this->findRedirect($old_url);
        if ($existing) {
            return;
        }

        $redirect = new self();
        $redirect->old_url = $this->normalizeUrl($old_url);
        $redirect->new_url = $this->normalizeUrl($new_url);
        $redirect->redirect_type = StPrettyUrlConfig::getDefaultRedirectType();
        $redirect->active = 1;
        $redirect->hits = 0;
        $redirect->add();
    }

    /**
     * Increment redirect hits
     */
    private function incrementHits($id_redirect)
    {
        $sql = 'UPDATE ' . _DB_PREFIX_ . 'st_pretty_url_redirects 
                SET hits = hits + 1 
                WHERE id_redirect = ' . (int)$id_redirect;
        
        Db::getInstance()->execute($sql);
    }

    /**
     * Get all redirects
     */
    public static function getAllRedirects($active_only = false, $limit = null, $offset = null)
    {
        $sql = 'SELECT * FROM ' . _DB_PREFIX_ . 'st_pretty_url_redirects';
        
        if ($active_only) {
            $sql .= ' WHERE active = 1';
        }
        
        $sql .= ' ORDER BY date_add DESC';
        
        if ($limit) {
            $sql .= ' LIMIT ' . (int)$limit;
            if ($offset) {
                $sql .= ' OFFSET ' . (int)$offset;
            }
        }
        
        return Db::getInstance()->executeS($sql);
    }

    /**
     * Delete redirect
     */
    public static function deleteRedirect($id_redirect)
    {
        $redirect = new self($id_redirect);
        return $redirect->delete();
    }

    /**
     * Bulk delete redirects
     */
    public static function bulkDeleteRedirects($ids)
    {
        if (!is_array($ids) || empty($ids)) {
            return false;
        }

        $ids = array_map('intval', $ids);
        $sql = 'DELETE FROM ' . _DB_PREFIX_ . 'st_pretty_url_redirects 
                WHERE id_redirect IN (' . implode(',', $ids) . ')';
        
        return Db::getInstance()->execute($sql);
    }
}

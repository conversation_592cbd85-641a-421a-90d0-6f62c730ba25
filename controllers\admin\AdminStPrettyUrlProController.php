<?php
/**
 * ST Pretty URL Pro Main Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStPrettyUrlProController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->context = Context::getContext();
        
        parent::__construct();
        
        $this->meta_title = $this->l('ST Pretty URL Pro');
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        parent::initContent();
        
        $this->content .= $this->renderDashboard();
    }

    /**
     * Render main dashboard
     */
    public function renderDashboard()
    {
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading">';
        $html .= '<i class="icon-dashboard"></i> ' . $this->l('ST Pretty URL Pro Dashboard');
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="alert alert-info">';
        $html .= '<h4>' . $this->l('Welcome to ST Pretty URL Pro!') . '</h4>';
        $html .= '<p>' . $this->l('Manage your URLs, redirects, meta tags, and SEO settings from the menu on the left.') . '</p>';
        $html .= '</div>';
        
        $html .= '<div class="row">';
        
        // Configuration
        $html .= '<div class="col-md-4">';
        $html .= '<div class="panel panel-default">';
        $html .= '<div class="panel-heading">';
        $html .= '<h3 class="panel-title"><i class="icon-cogs"></i> ' . $this->l('Configuration') . '</h3>';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        $html .= '<p>' . $this->l('Configure module settings, URL patterns, and advanced options.') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStPrettyUrlProConfig') . '" class="btn btn-primary">' . $this->l('Configure') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Redirects
        $html .= '<div class="col-md-4">';
        $html .= '<div class="panel panel-default">';
        $html .= '<div class="panel-heading">';
        $html .= '<h3 class="panel-title"><i class="icon-share"></i> ' . $this->l('Redirects') . '</h3>';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        $html .= '<p>' . $this->l('Manage URL redirects and track their performance.') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStRedirectManager') . '" class="btn btn-primary">' . $this->l('Manage Redirects') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Meta Tags
        $html .= '<div class="col-md-4">';
        $html .= '<div class="panel panel-default">';
        $html .= '<div class="panel-heading">';
        $html .= '<h3 class="panel-title"><i class="icon-tags"></i> ' . $this->l('Meta Tags') . '</h3>';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        $html .= '<p>' . $this->l('Configure meta tag templates for automatic SEO optimization.') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStMetaTags') . '" class="btn btn-primary">' . $this->l('Manage Meta Tags') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>'; // End first row
        
        $html .= '<div class="row">';
        
        // Sitemap
        $html .= '<div class="col-md-4">';
        $html .= '<div class="panel panel-default">';
        $html .= '<div class="panel-heading">';
        $html .= '<h3 class="panel-title"><i class="icon-sitemap"></i> ' . $this->l('Sitemap') . '</h3>';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        $html .= '<p>' . $this->l('Generate and manage XML sitemaps for search engines.') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStSitemap') . '" class="btn btn-primary">' . $this->l('Manage Sitemap') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Duplicate URLs
        $html .= '<div class="col-md-4">';
        $html .= '<div class="panel panel-default">';
        $html .= '<div class="panel-heading">';
        $html .= '<h3 class="panel-title"><i class="icon-warning"></i> ' . $this->l('Duplicate URLs') . '</h3>';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        $html .= '<p>' . $this->l('Scan for and resolve duplicate URL conflicts.') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStDuplicateUrls') . '" class="btn btn-primary">' . $this->l('Check Duplicates') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Open Graph
        $html .= '<div class="col-md-4">';
        $html .= '<div class="panel panel-default">';
        $html .= '<div class="panel-heading">';
        $html .= '<h3 class="panel-title"><i class="icon-share-alt"></i> ' . $this->l('Open Graph') . '</h3>';
        $html .= '</div>';
        $html .= '<div class="panel-body">';
        $html .= '<p>' . $this->l('Configure Open Graph tags for social media sharing.') . '</p>';
        $html .= '<a href="' . $this->context->link->getAdminLink('AdminStOpenGraph') . '" class="btn btn-primary">' . $this->l('Configure Open Graph') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>'; // End second row
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Set media
     */
    public function setMedia()
    {
        parent::setMedia();
        
        $this->addCSS($this->module->getPathUri() . 'views/css/admin.css');
        $this->addJS($this->module->getPathUri() . 'views/js/admin.js');
    }
}

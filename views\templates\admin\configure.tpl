{*
* ST Pretty URL Pro Admin Configuration Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="panel">
    <div class="panel-heading">
        <i class="icon-cogs"></i>
        {l s='ST Pretty URL Pro Configuration' mod='stprettyurlpro'}
    </div>
    <div class="panel-body">
        <div class="alert alert-info">
            <h4>{l s='Welcome to ST Pretty URL Pro!' mod='stprettyurlpro'}</h4>
            <p>{l s='This module provides advanced URL management, SEO optimization, and redirect management for your PrestaShop store.' mod='stprettyurlpro'}</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h4><i class="icon-link"></i> {l s='URL Management' mod='stprettyurlpro'}</h4>
                <ul>
                    <li>{l s='Remove IDs from product and category URLs' mod='stprettyurlpro'}</li>
                    <li>{l s='Custom URL patterns for different page types' mod='stprettyurlpro'}</li>
                    <li>{l s='Automatic redirect creation for old URLs' mod='stprettyurlpro'}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4><i class="icon-search"></i> {l s='SEO Features' mod='stprettyurlpro'}</h4>
                <ul>
                    <li>{l s='Automatic meta tag generation with variables' mod='stprettyurlpro'}</li>
                    <li>{l s='XML sitemap generation' mod='stprettyurlpro'}</li>
                    <li>{l s='Dynamic robots.txt creation' mod='stprettyurlpro'}</li>
                    <li>{l s='Open Graph meta tags for social media' mod='stprettyurlpro'}</li>
                </ul>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h4><i class="icon-share"></i> {l s='Redirect Management' mod='stprettyurlpro'}</h4>
                <ul>
                    <li>{l s='301, 302, and 303 redirect support' mod='stprettyurlpro'}</li>
                    <li>{l s='Bulk import/export functionality' mod='stprettyurlpro'}</li>
                    <li>{l s='Hit tracking and analytics' mod='stprettyurlpro'}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4><i class="icon-warning"></i> {l s='Quality Control' mod='stprettyurlpro'}</h4>
                <ul>
                    <li>{l s='Duplicate URL detection and reporting' mod='stprettyurlpro'}</li>
                    <li>{l s='URL validation and cleanup' mod='stprettyurlpro'}</li>
                    <li>{l s='Performance monitoring' mod='stprettyurlpro'}</li>
                </ul>
            </div>
        </div>

        <hr>

        <div class="alert alert-warning">
            <h4><i class="icon-exclamation-triangle"></i> {l s='Important Notes' mod='stprettyurlpro'}</h4>
            <ul>
                <li>{l s='Always backup your database before making changes' mod='stprettyurlpro'}</li>
                <li>{l s='Test URL changes on a staging environment first' mod='stprettyurlpro'}</li>
                <li>{l s='Monitor your site for 404 errors after enabling URL cleaning' mod='stprettyurlpro'}</li>
                <li>{l s='Clear your cache after configuration changes' mod='stprettyurlpro'}</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-12">
                <h4><i class="icon-book"></i> {l s='Quick Start Guide' mod='stprettyurlpro'}</h4>
                <ol>
                    <li>{l s='Configure basic settings below' mod='stprettyurlpro'}</li>
                    <li>{l s='Set up URL patterns in the URL Patterns section' mod='stprettyurlpro'}</li>
                    <li>{l s='Configure meta tag templates for automatic SEO' mod='stprettyurlpro'}</li>
                    <li>{l s='Enable sitemap generation and submit to search engines' mod='stprettyurlpro'}</li>
                    <li>{l s='Monitor duplicate URLs and redirects regularly' mod='stprettyurlpro'}</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="panel">
    <div class="panel-heading">
        <i class="icon-bar-chart"></i>
        {l s='Module Statistics' mod='stprettyurlpro'}
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-3">
                <div class="kpi-container">
                    <div class="kpi kpi-primary">
                        <span class="kpi-title">{l s='Active Redirects' mod='stprettyurlpro'}</span>
                        <span class="kpi-value" id="active-redirects">-</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-container">
                    <div class="kpi kpi-success">
                        <span class="kpi-title">{l s='Clean URLs' mod='stprettyurlpro'}</span>
                        <span class="kpi-value" id="clean-urls">-</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-container">
                    <div class="kpi kpi-warning">
                        <span class="kpi-title">{l s='Duplicate URLs' mod='stprettyurlpro'}</span>
                        <span class="kpi-value" id="duplicate-urls">-</span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-container">
                    <div class="kpi kpi-info">
                        <span class="kpi-title">{l s='Sitemap URLs' mod='stprettyurlpro'}</span>
                        <span class="kpi-value" id="sitemap-urls">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Load statistics via AJAX
    loadModuleStatistics();
    
    function loadModuleStatistics() {
        $.ajax({
            url: '{$module_dir}ajax/statistics.php',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    $('#active-redirects').text(data.stats.active_redirects || 0);
                    $('#clean-urls').text(data.stats.clean_urls || 0);
                    $('#duplicate-urls').text(data.stats.duplicate_urls || 0);
                    $('#sitemap-urls').text(data.stats.sitemap_urls || 0);
                }
            },
            error: function() {
                console.log('Failed to load module statistics');
            }
        });
    }
    
    // Refresh statistics every 30 seconds
    setInterval(loadModuleStatistics, 30000);
});
</script>

<style>
.kpi-container {
    margin-bottom: 20px;
}

.kpi {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.kpi-primary {
    border-left: 4px solid #337ab7;
}

.kpi-success {
    border-left: 4px solid #5cb85c;
}

.kpi-warning {
    border-left: 4px solid #f0ad4e;
}

.kpi-info {
    border-left: 4px solid #5bc0de;
}

.kpi-title {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    font-weight: bold;
}

.kpi-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.alert h4 {
    margin-top: 0;
}

.alert ul {
    margin-bottom: 0;
}
</style>

/**
 * ST Pretty URL Pro Admin JavaScript
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

(function() {
    'use strict';

    var StPrettyUrlProAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initTooltips();
            this.initFormValidation();
            this.loadStatistics();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Bulk import form
            $(document).on('click', '.bulk-import-btn', function(e) {
                e.preventDefault();
                StPrettyUrlProAdmin.showBulkImportModal();
            });

            // Test redirect functionality
            $(document).on('click', '.test-redirect-btn', function(e) {
                e.preventDefault();
                var url = $(this).data('url');
                StPrettyUrlProAdmin.testRedirect(url);
            });

            // Generate sitemap
            $(document).on('click', '.generate-sitemap-btn', function(e) {
                e.preventDefault();
                StPrettyUrlProAdmin.generateSitemap();
            });

            // Scan duplicates
            $(document).on('click', '.scan-duplicates-btn', function(e) {
                e.preventDefault();
                StPrettyUrlProAdmin.scanDuplicates();
            });

            // Clear cache
            $(document).on('click', '.clear-cache-btn', function(e) {
                e.preventDefault();
                StPrettyUrlProAdmin.clearCache();
            });

            // Variable insertion helpers
            $(document).on('click', '.insert-variable', function(e) {
                e.preventDefault();
                var variable = $(this).data('variable');
                var target = $(this).data('target');
                StPrettyUrlProAdmin.insertVariable(variable, target);
            });

            // Form auto-save
            $(document).on('change', '.auto-save', function() {
                StPrettyUrlProAdmin.autoSave($(this));
            });
        },

        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            $('[data-toggle="tooltip"]').tooltip();
            
            // Custom tooltips for variables
            $('.variable-help').tooltip({
                placement: 'top',
                html: true
            });
        },

        /**
         * Initialize form validation
         */
        initFormValidation: function() {
            // URL validation
            $('input[name*="url"]').on('blur', function() {
                var url = $(this).val();
                if (url && !StPrettyUrlProAdmin.isValidUrl(url)) {
                    $(this).addClass('error');
                    StPrettyUrlProAdmin.showError('Invalid URL format');
                } else {
                    $(this).removeClass('error');
                }
            });

            // Priority validation
            $('input[name="priority"]').on('blur', function() {
                var priority = parseFloat($(this).val());
                if (priority < 0 || priority > 1) {
                    $(this).addClass('error');
                    StPrettyUrlProAdmin.showError('Priority must be between 0.0 and 1.0');
                } else {
                    $(this).removeClass('error');
                }
            });
        },

        /**
         * Load statistics via AJAX
         */
        loadStatistics: function() {
            if ($('.module-statistics').length === 0) {
                return;
            }

            $.ajax({
                url: stprettyurlpro_ajax_url,
                type: 'GET',
                data: {
                    action: 'getStatistics'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        StPrettyUrlProAdmin.updateStatistics(response.data);
                    }
                },
                error: function() {
                    console.log('Failed to load statistics');
                }
            });
        },

        /**
         * Update statistics display
         */
        updateStatistics: function(data) {
            $.each(data, function(key, value) {
                $('.stat-' + key).text(value);
            });
        },

        /**
         * Show bulk import modal
         */
        showBulkImportModal: function() {
            var modal = $('#bulk-import-modal');
            if (modal.length === 0) {
                // Create modal if it doesn't exist
                modal = this.createBulkImportModal();
            }
            modal.modal('show');
        },

        /**
         * Create bulk import modal
         */
        createBulkImportModal: function() {
            var modalHtml = '<div class="modal fade" id="bulk-import-modal" tabindex="-1">' +
                '<div class="modal-dialog modal-lg">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal">&times;</button>' +
                '<h4 class="modal-title">Bulk Import Redirects</h4>' +
                '</div>' +
                '<div class="modal-body">' +
                '<form id="bulk-import-form">' +
                '<div class="form-group">' +
                '<label>Redirects (one per line: old_url|new_url|type)</label>' +
                '<textarea class="form-control" name="bulk_redirects" rows="10" placeholder="/old-page|/new-page|301"></textarea>' +
                '</div>' +
                '</form>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>' +
                '<button type="button" class="btn btn-primary" onclick="StPrettyUrlProAdmin.processBulkImport()">Import</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            
            $('body').append(modalHtml);
            return $('#bulk-import-modal');
        },

        /**
         * Process bulk import
         */
        processBulkImport: function() {
            var data = $('#bulk-import-form').serialize();
            
            $.ajax({
                url: stprettyurlpro_ajax_url,
                type: 'POST',
                data: data + '&action=bulkImport',
                dataType: 'json',
                beforeSend: function() {
                    $('.modal-footer .btn-primary').addClass('loading').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        StPrettyUrlProAdmin.showSuccess(response.message);
                        $('#bulk-import-modal').modal('hide');
                        location.reload();
                    } else {
                        StPrettyUrlProAdmin.showError(response.message);
                    }
                },
                error: function() {
                    StPrettyUrlProAdmin.showError('Import failed');
                },
                complete: function() {
                    $('.modal-footer .btn-primary').removeClass('loading').prop('disabled', false);
                }
            });
        },

        /**
         * Test redirect
         */
        testRedirect: function(url) {
            window.open(url, '_blank');
        },

        /**
         * Generate sitemap
         */
        generateSitemap: function() {
            $.ajax({
                url: stprettyurlpro_ajax_url,
                type: 'POST',
                data: {
                    action: 'generateSitemap'
                },
                dataType: 'json',
                beforeSend: function() {
                    $('.generate-sitemap-btn').addClass('loading').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        StPrettyUrlProAdmin.showSuccess('Sitemap generated successfully');
                    } else {
                        StPrettyUrlProAdmin.showError(response.message || 'Failed to generate sitemap');
                    }
                },
                error: function() {
                    StPrettyUrlProAdmin.showError('Failed to generate sitemap');
                },
                complete: function() {
                    $('.generate-sitemap-btn').removeClass('loading').prop('disabled', false);
                }
            });
        },

        /**
         * Scan for duplicates
         */
        scanDuplicates: function() {
            $.ajax({
                url: stprettyurlpro_ajax_url,
                type: 'POST',
                data: {
                    action: 'scanDuplicates'
                },
                dataType: 'json',
                beforeSend: function() {
                    $('.scan-duplicates-btn').addClass('loading').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        StPrettyUrlProAdmin.showSuccess(response.message);
                        if (response.duplicates > 0) {
                            setTimeout(function() {
                                window.location.href = stprettyurlpro_duplicate_url;
                            }, 2000);
                        }
                    } else {
                        StPrettyUrlProAdmin.showError(response.message || 'Scan failed');
                    }
                },
                error: function() {
                    StPrettyUrlProAdmin.showError('Scan failed');
                },
                complete: function() {
                    $('.scan-duplicates-btn').removeClass('loading').prop('disabled', false);
                }
            });
        },

        /**
         * Clear cache
         */
        clearCache: function() {
            $.ajax({
                url: stprettyurlpro_ajax_url,
                type: 'POST',
                data: {
                    action: 'clearCache'
                },
                dataType: 'json',
                beforeSend: function() {
                    $('.clear-cache-btn').addClass('loading').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        StPrettyUrlProAdmin.showSuccess('Cache cleared successfully');
                    } else {
                        StPrettyUrlProAdmin.showError(response.message || 'Failed to clear cache');
                    }
                },
                error: function() {
                    StPrettyUrlProAdmin.showError('Failed to clear cache');
                },
                complete: function() {
                    $('.clear-cache-btn').removeClass('loading').prop('disabled', false);
                }
            });
        },

        /**
         * Insert variable into textarea
         */
        insertVariable: function(variable, targetSelector) {
            var target = $(targetSelector);
            if (target.length === 0) {
                return;
            }

            var currentValue = target.val();
            var cursorPos = target[0].selectionStart;
            var newValue = currentValue.substring(0, cursorPos) + variable + currentValue.substring(cursorPos);
            
            target.val(newValue);
            target.focus();
            target[0].setSelectionRange(cursorPos + variable.length, cursorPos + variable.length);
        },

        /**
         * Auto-save functionality
         */
        autoSave: function(element) {
            var form = element.closest('form');
            var data = form.serialize();
            
            $.ajax({
                url: stprettyurlpro_ajax_url,
                type: 'POST',
                data: data + '&action=autoSave',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        element.addClass('saved');
                        setTimeout(function() {
                            element.removeClass('saved');
                        }, 2000);
                    }
                }
            });
        },

        /**
         * Validate URL format
         */
        isValidUrl: function(url) {
            if (url.startsWith('/')) {
                return true; // Relative URL
            }
            
            try {
                new URL(url);
                return true;
            } catch (e) {
                return false;
            }
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            this.showMessage(message, 'success');
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.showMessage(message, 'danger');
        },

        /**
         * Show message
         */
        showMessage: function(message, type) {
            var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible">' +
                '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                message +
                '</div>';
            
            $('.content-header').after(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        },

        /**
         * Format numbers for display
         */
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        StPrettyUrlProAdmin.init();
    });

    // Make available globally
    window.StPrettyUrlProAdmin = StPrettyUrlProAdmin;

})();

<?php
/**
 * ST Pretty URL Pro Module - Uninstall SQL
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

$sql = array();

// Drop all module tables
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_redirects`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_meta_templates`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_meta_templates_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_opengraph`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_opengraph_lang`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_duplicates`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_config`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_patterns`';
$sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_pretty_url_sitemap`';

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}

<?php
/**
 * ST Pretty URL Pro AJAX Statistics Endpoint
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

// Include PrestaShop configuration
$config_path = dirname(__FILE__) . '/../../../../config/config.inc.php';
if (file_exists($config_path)) {
    require_once $config_path;
} else {
    // Alternative path for different PrestaShop structures
    require_once dirname(__FILE__) . '/../../../config/config.inc.php';
}

// Security check
if (!defined('_PS_VERSION_')) {
    exit('Access denied');
}

// Check if user is logged in admin
if (!Context::getContext()->employee || !Context::getContext()->employee->id) {
    exit(json_encode(array('success' => false, 'error' => 'Access denied')));
}

// Include module classes
require_once dirname(__FILE__) . '/../classes/StRedirectManager.php';
require_once dirname(__FILE__) . '/../classes/StDuplicateUrlDetector.php';
require_once dirname(__FILE__) . '/../classes/StSitemapGenerator.php';

try {
    $stats = array();
    
    // Get active redirects count
    $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'st_pretty_url_redirects WHERE active = 1';
    $stats['active_redirects'] = (int)Db::getInstance()->getValue($sql);
    
    // Get clean URLs count (estimate based on products and categories)
    $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'product p
            LEFT JOIN ' . _DB_PREFIX_ . 'product_shop ps ON (p.id_product = ps.id_product)
            WHERE p.active = 1 AND ps.active = 1';
    $product_count = (int)Db::getInstance()->getValue($sql);
    
    $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'category c
            LEFT JOIN ' . _DB_PREFIX_ . 'category_shop cs ON (c.id_category = cs.id_category)
            WHERE c.active = 1 AND c.id_category > 2';
    $category_count = (int)Db::getInstance()->getValue($sql);
    
    $stats['clean_urls'] = $product_count + $category_count;
    
    // Get duplicate URLs count
    $duplicate_stats = StDuplicateUrlDetector::getDuplicateStats();
    $stats['duplicate_urls'] = $duplicate_stats['pending'];
    
    // Get sitemap URLs count
    $sitemap_generator = new StSitemapGenerator();
    $sitemap_stats = $sitemap_generator->getSitemapStats();
    $stats['sitemap_urls'] = $sitemap_stats['total_urls'];
    
    // Return success response
    echo json_encode(array(
        'success' => true,
        'stats' => $stats
    ));
    
} catch (Exception $e) {
    // Return error response
    echo json_encode(array(
        'success' => false,
        'error' => $e->getMessage()
    ));
}

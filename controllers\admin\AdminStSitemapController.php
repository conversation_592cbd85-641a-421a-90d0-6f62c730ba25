<?php
/**
 * ST Pretty URL Pro Sitemap Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminStSitemapController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'st_pretty_url_sitemap';
        $this->className = 'StSitemapGenerator';
		$this->identifier = 'id_sitemap';
        $this->lang = false;
        $this->addRowAction('edit');
        $this->context = Context::getContext();

		parent::__construct();
		
        $this->fields_list = array(
            'id_sitemap' => array(
                'title' => $this->module->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'page_type' => array(
                'title' => $this->module->l('Page Type'),
                'width' => 'auto',
                'type' => 'select',
                'list' => array(
                    'product' => 'Product',
                    'category' => 'Category',
                    'cms' => 'CMS',
                    'manufacturer' => 'Manufacturer',
                    'supplier' => 'Supplier'
                ),
                'filter_key' => 'a!page_type'
            ),
            'include_in_sitemap' => array(
                'title' => $this->module->l('Include'),
                'align' => 'center',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'priority' => array(
                'title' => $this->module->l('Priority'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'changefreq' => array(
                'title' => $this->module->l('Change Frequency'),
                'align' => 'center',
                'class' => 'fixed-width-sm'
            ),
            'active' => array(
                'title' => $this->module->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            )
        );

        parent::__construct();

        $this->meta_title = $this->module->l('Sitemap Configuration');
    }

    /**
     * Render form
     */
    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->module->l('Sitemap Configuration'),
                'icon' => 'icon-sitemap'
            ),
            'input' => array(
                array(
                    'type' => 'select',
                    'label' => $this->module->l('Page Type'),
                    'name' => 'page_type',
                    'required' => true,
                    'options' => array(
                        'query' => array(
                            array('id' => 'product', 'name' => $this->module->l('Product')),
                            array('id' => 'category', 'name' => $this->module->l('Category')),
                            array('id' => 'cms', 'name' => $this->module->l('CMS')),
                            array('id' => 'manufacturer', 'name' => $this->module->l('Manufacturer')),
                            array('id' => 'supplier', 'name' => $this->module->l('Supplier')),
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->module->l('Select the page type for this sitemap configuration')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->module->l('Include in Sitemap'),
                    'name' => 'include_in_sitemap',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'include_on',
                            'value' => 1,
                            'label' => $this->module->l('Yes')
                        ),
                        array(
                            'id' => 'include_off',
                            'value' => 0,
                            'label' => $this->module->l('No')
                        )
                    ),
                    'desc' => $this->module->l('Include this page type in the XML sitemap')
                ),
                array(
                    'type' => 'select',
                    'label' => $this->module->l('Priority'),
                    'name' => 'priority',
                    'options' => array(
                        'query' => array(
                            array('id' => '1.0', 'name' => '1.0 - ' . $this->module->l('Highest')),
                            array('id' => '0.9', 'name' => '0.9 - ' . $this->module->l('Very High')),
                            array('id' => '0.8', 'name' => '0.8 - ' . $this->module->l('High')),
                            array('id' => '0.7', 'name' => '0.7 - ' . $this->module->l('Above Normal')),
                            array('id' => '0.6', 'name' => '0.6 - ' . $this->module->l('Normal')),
                            array('id' => '0.5', 'name' => '0.5 - ' . $this->module->l('Default')),
                            array('id' => '0.4', 'name' => '0.4 - ' . $this->module->l('Below Normal')),
                            array('id' => '0.3', 'name' => '0.3 - ' . $this->module->l('Low')),
                            array('id' => '0.2', 'name' => '0.2 - ' . $this->module->l('Very Low')),
                            array('id' => '0.1', 'name' => '0.1 - ' . $this->module->l('Lowest')),
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->module->l('Priority of this page type in the sitemap (0.0 to 1.0)')
                ),
                array(
                    'type' => 'select',
                    'label' => $this->module->l('Change Frequency'),
                    'name' => 'changefreq',
                    'options' => array(
                        'query' => array(
                            array('id' => 'always', 'name' => $this->module->l('Always')),
                            array('id' => 'hourly', 'name' => $this->module->l('Hourly')),
                            array('id' => 'daily', 'name' => $this->module->l('Daily')),
                            array('id' => 'weekly', 'name' => $this->module->l('Weekly')),
                            array('id' => 'monthly', 'name' => $this->module->l('Monthly')),
                            array('id' => 'yearly', 'name' => $this->module->l('Yearly')),
                            array('id' => 'never', 'name' => $this->module->l('Never')),
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'desc' => $this->module->l('How frequently the page is likely to change')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->module->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->module->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->module->l('Disabled')
                        )
                    ),
                    'desc' => $this->module->l('Enable or disable this sitemap configuration')
                )
            ),
            'submit' => array(
                'title' => $this->module->l('Save'),
            )
        );

        if (!($obj = $this->module->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    /**
     * Initialize content
     */
    public function initContent()
    {
        if ($this->display == 'add' || $this->display == 'edit') {
            $this->content .= $this->renderForm();
        } else {
            $this->content .= $this->renderSitemapTools();
            $this->content .= $this->renderSitemapStats();
            $this->content .= $this->renderList();
        }

        $this->context->smarty->assign(array(
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ));
    }

    /**
     * Render sitemap tools
     */
    public function renderSitemapTools()
    {
        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-wrench"></i> ' . $this->module->l('Sitemap Tools') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-4">';
        $html .= '<h4>' . $this->module->l('Generate Sitemap') . '</h4>';
        $html .= '<p>' . $this->module->l('Generate and save the XML sitemap file') . '</p>';
        $html .= '<a href="' . self::$currentIndex . '&action=generateSitemap&token=' . $this->token . '" class="btn btn-primary">' . $this->module->l('Generate Sitemap') . '</a>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-4">';
        $html .= '<h4>' . $this->module->l('View Sitemap') . '</h4>';
        $html .= '<p>' . $this->module->l('View the current XML sitemap') . '</p>';
        $html .= '<a href="' . $this->context->shop->getBaseURL(true) . 'sitemap.xml" target="_blank" class="btn btn-info">' . $this->module->l('View Sitemap') . '</a>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-4">';
        $html .= '<h4>' . $this->module->l('Submit to Google') . '</h4>';
        $html .= '<p>' . $this->module->l('Submit your sitemap to Google Search Console') . '</p>';
        $html .= '<a href="https://search.google.com/search-console" target="_blank" class="btn btn-success">' . $this->module->l('Google Search Console') . '</a>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Render sitemap statistics
     */
    public function renderSitemapStats()
    {
        $sitemap_generator = new StSitemapGenerator();
        $stats = $sitemap_generator->getSitemapStats();

        $html = '<div class="panel">';
        $html .= '<div class="panel-heading"><i class="icon-bar-chart"></i> ' . $this->module->l('Sitemap Statistics') . '</div>';
        $html .= '<div class="panel-body">';
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-2">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-primary">';
        $html .= '<span class="kpi-title">' . $this->module->l('Total URLs') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['total_urls'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-2">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-success">';
        $html .= '<span class="kpi-title">' . $this->module->l('Products') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['products'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-2">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-info">';
        $html .= '<span class="kpi-title">' . $this->module->l('Categories') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['categories'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-2">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-warning">';
        $html .= '<span class="kpi-title">' . $this->module->l('CMS Pages') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['cms_pages'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-2">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-default">';
        $html .= '<span class="kpi-title">' . $this->module->l('Manufacturers') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['manufacturers'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '<div class="col-md-2">';
        $html .= '<div class="kpi-container">';
        $html .= '<div class="kpi kpi-default">';
        $html .= '<span class="kpi-title">' . $this->module->l('Suppliers') . '</span>';
        $html .= '<span class="kpi-value">' . $stats['suppliers'] . '</span>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Process generate sitemap
     */
    public function processGenerateSitemap()
    {
        $sitemap_generator = new StSitemapGenerator();
        if ($sitemap_generator->saveSitemapToFile()) {
            $this->confirmations[] = $this->module->l('Sitemap generated successfully');
        } else {
            $this->errors[] = $this->module->l('Failed to generate sitemap');
        }
    }

    /**
     * Post process
     */
    public function postProcess()
    {
        if (Tools::getValue('action') === 'generateSitemap') {
            $this->processGenerateSitemap();
        }

        return parent::postProcess();
    }

    /**
     * Initialize page header toolbar
     */
    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['generate_sitemap'] = array(
                'href' => self::$currentIndex . '&action=generateSitemap&token=' . $this->token,
                'desc' => $this->module->l('Generate sitemap', null, null, false),
                'icon' => 'process-icon-refresh'
            );
        }

        parent::initPageHeaderToolbar();
    }
}

/**
 * ST Pretty URL Pro Admin Styles
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* Dashboard styles */
.stprettyurlpro-dashboard .panel {
    margin-bottom: 20px;
}

.stprettyurlpro-dashboard .panel-title {
    font-size: 16px;
    font-weight: bold;
}

.stprettyurlpro-dashboard .panel-body {
    min-height: 120px;
}

/* KPI styles */
.kpi-container {
    margin-bottom: 20px;
}

.kpi {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.kpi-primary {
    border-left: 4px solid #337ab7;
}

.kpi-success {
    border-left: 4px solid #5cb85c;
}

.kpi-warning {
    border-left: 4px solid #f0ad4e;
}

.kpi-info {
    border-left: 4px solid #5bc0de;
}

.kpi-default {
    border-left: 4px solid #777;
}

.kpi-title {
    display: block;
    font-size: 11px;
    color: #666;
    margin-bottom: 5px;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.kpi-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

/* Form enhancements */
.stprettyurlpro-form .form-group {
    margin-bottom: 20px;
}

.stprettyurlpro-form .help-block {
    font-size: 12px;
    color: #737373;
    margin-top: 5px;
}

/* Variable help table */
.variables-help table {
    font-size: 12px;
}

.variables-help code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
}

/* Status badges */
.status-pending {
    background-color: #f0ad4e;
    color: white;
}

.status-resolved {
    background-color: #5cb85c;
    color: white;
}

.status-ignored {
    background-color: #777;
    color: white;
}

/* Tools section */
.tools-section {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.tools-section h4 {
    margin-top: 0;
    color: #333;
}

.tools-section .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Statistics section */
.stats-section .row {
    margin-bottom: 20px;
}

.stats-section .stat-item {
    text-align: center;
    padding: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.stats-section .stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #337ab7;
    display: block;
}

.stats-section .stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    margin-top: 5px;
}

/* Redirect manager specific */
.redirect-hits {
    font-weight: bold;
    color: #5cb85c;
}

.redirect-type-301 {
    color: #5cb85c;
}

.redirect-type-302 {
    color: #f0ad4e;
}

.redirect-type-303 {
    color: #5bc0de;
}

/* Meta tags specific */
.meta-template-preview {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    font-family: monospace;
    font-size: 12px;
}

/* Duplicate URLs specific */
.duplicate-count {
    font-weight: bold;
    color: #d9534f;
}

.duplicate-url {
    font-family: monospace;
    font-size: 12px;
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Open Graph specific */
.og-preview {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
    margin-top: 15px;
}

.og-preview h4 {
    margin: 0 0 10px 0;
    color: #1877f2;
    font-size: 16px;
}

.og-preview p {
    margin: 0;
    color: #606770;
    font-size: 14px;
}

.og-preview .og-url {
    color: #606770;
    font-size: 12px;
    text-transform: uppercase;
    margin-top: 5px;
}

/* Sitemap specific */
.sitemap-stats .kpi {
    min-height: 60px;
}

.sitemap-stats .kpi-title {
    font-size: 10px;
}

.sitemap-stats .kpi-value {
    font-size: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kpi-value {
        font-size: 20px;
    }
    
    .kpi-title {
        font-size: 10px;
    }
    
    .tools-section .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ccc;
    border-top-color: #337ab7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error messages */
.alert-module {
    margin-bottom: 20px;
}

.alert-module .alert-title {
    font-weight: bold;
    margin-bottom: 5px;
}

/* Module branding */
.module-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.module-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 300;
}

.module-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
}

/* Advanced configuration */
.advanced-config {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.advanced-config .icon {
    color: #856404;
    margin-right: 10px;
}

/* Debug mode indicators */
.debug-mode-active {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 20px;
    color: #0c5460;
}

.debug-mode-active .icon {
    margin-right: 10px;
}

<?php
/**
 * ST Pretty URL Pro Sitemap Generator Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StSitemapGenerator
{
    private $context;
    private $base_url;
    private $url_cleaner;

    public function __construct()
    {
        $this->context = Context::getContext();
        $this->base_url = $this->context->shop->getBaseURL(true);
        $this->url_cleaner = StUrlCleaner::getInstance();
    }

    /**
     * Generate complete XML sitemap
     */
    public function generateSitemap()
    {
        if (!Configuration::get('STPRETTYURLPRO_SITEMAP_ENABLED')) {
            return '';
        }

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add homepage
        $xml .= $this->generateUrlEntry($this->base_url, date('c'), 'daily', '1.0');

        // Add categories
        $xml .= $this->generateCategoryUrls();

        // Add products
        $xml .= $this->generateProductUrls();

        // Add CMS pages
        $xml .= $this->generateCmsUrls();

        // Add manufacturers
        $xml .= $this->generateManufacturerUrls();

        // Add suppliers
        $xml .= $this->generateSupplierUrls();

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate category URLs for sitemap
     */
    private function generateCategoryUrls()
    {
        $config = $this->getSitemapConfig('category');
        if (!$config || !$config['include_in_sitemap']) {
            return '';
        }

        $xml = '';
        $categories = Category::getCategories($this->context->language->id, true, false);

        foreach ($categories as $category_group) {
            foreach ($category_group as $category) {
                if ($category['id_category'] <= 2) { // Skip root and home categories
                    continue;
                }

                $clean_url = $this->url_cleaner->cleanCategoryUrl($category['id_category']);
                $url = $clean_url ? $this->base_url . $clean_url : $this->context->link->getCategoryLink($category['id_category']);

                $lastmod = $category['date_upd'] ? date('c', strtotime($category['date_upd'])) : date('c');
                
                $xml .= $this->generateUrlEntry(
                    $url,
                    $lastmod,
                    $config['changefreq'],
                    $config['priority']
                );
            }
        }

        return $xml;
    }

    /**
     * Generate product URLs for sitemap
     */
    private function generateProductUrls()
    {
        $config = $this->getSitemapConfig('product');
        if (!$config || !$config['include_in_sitemap']) {
            return '';
        }

        $xml = '';
        $sql = 'SELECT p.id_product, pl.link_rewrite, p.date_upd
                FROM ' . _DB_PREFIX_ . 'product p
                LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                LEFT JOIN ' . _DB_PREFIX_ . 'product_shop ps ON (p.id_product = ps.id_product AND ps.id_shop = ' . (int)$this->context->shop->id . ')
                WHERE p.active = 1 AND ps.active = 1
                ORDER BY p.id_product';

        $products = Db::getInstance()->executeS($sql);

        foreach ($products as $product) {
            $clean_url = $this->url_cleaner->cleanProductUrl($product['id_product']);
            $url = $clean_url ? $this->base_url . $clean_url : $this->context->link->getProductLink($product['id_product']);

            $lastmod = $product['date_upd'] ? date('c', strtotime($product['date_upd'])) : date('c');
            
            $xml .= $this->generateUrlEntry(
                $url,
                $lastmod,
                $config['changefreq'],
                $config['priority']
            );
        }

        return $xml;
    }

    /**
     * Generate CMS URLs for sitemap
     */
    private function generateCmsUrls()
    {
        $config = $this->getSitemapConfig('cms');
        if (!$config || !$config['include_in_sitemap']) {
            return '';
        }

        $xml = '';
        $cms_pages = CMS::getCMSPages($this->context->language->id, null, true);

        foreach ($cms_pages as $cms) {
            $clean_url = $this->url_cleaner->cleanCmsUrl($cms['id_cms']);
            $url = $clean_url ? $this->base_url . $clean_url : $this->context->link->getCMSLink($cms['id_cms']);

            $lastmod = date('c');
            
            $xml .= $this->generateUrlEntry(
                $url,
                $lastmod,
                $config['changefreq'],
                $config['priority']
            );
        }

        return $xml;
    }

    /**
     * Generate manufacturer URLs for sitemap
     */
    private function generateManufacturerUrls()
    {
        $config = $this->getSitemapConfig('manufacturer');
        if (!$config || !$config['include_in_sitemap']) {
            return '';
        }

        $xml = '';
        $manufacturers = Manufacturer::getManufacturers(false, $this->context->language->id);

        foreach ($manufacturers as $manufacturer) {
            $url = $this->context->link->getManufacturerLink($manufacturer['id_manufacturer']);
            $lastmod = date('c');
            
            $xml .= $this->generateUrlEntry(
                $url,
                $lastmod,
                $config['changefreq'],
                $config['priority']
            );
        }

        return $xml;
    }

    /**
     * Generate supplier URLs for sitemap
     */
    private function generateSupplierUrls()
    {
        $config = $this->getSitemapConfig('supplier');
        if (!$config || !$config['include_in_sitemap']) {
            return '';
        }

        $xml = '';
        $suppliers = Supplier::getSuppliers(false, $this->context->language->id);

        foreach ($suppliers as $supplier) {
            $url = $this->context->link->getSupplierLink($supplier['id_supplier']);
            $lastmod = date('c');
            
            $xml .= $this->generateUrlEntry(
                $url,
                $lastmod,
                $config['changefreq'],
                $config['priority']
            );
        }

        return $xml;
    }

    /**
     * Generate single URL entry for sitemap
     */
    private function generateUrlEntry($url, $lastmod, $changefreq, $priority)
    {
        $xml = '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url, ENT_XML1, 'UTF-8') . '</loc>' . "\n";
        $xml .= '    <lastmod>' . $lastmod . '</lastmod>' . "\n";
        $xml .= '    <changefreq>' . $changefreq . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $priority . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";

        return $xml;
    }

    /**
     * Get sitemap configuration for page type
     */
    private function getSitemapConfig($page_type)
    {
        $configs = StPrettyUrlConfig::getSitemapConfig($page_type);
        
        if (!empty($configs)) {
            return $configs[0];
        }

        // Default configuration
        $defaults = array(
            'product' => array('include_in_sitemap' => 1, 'priority' => '0.8', 'changefreq' => 'weekly'),
            'category' => array('include_in_sitemap' => 1, 'priority' => '0.6', 'changefreq' => 'weekly'),
            'cms' => array('include_in_sitemap' => 1, 'priority' => '0.4', 'changefreq' => 'monthly'),
            'manufacturer' => array('include_in_sitemap' => 1, 'priority' => '0.5', 'changefreq' => 'monthly'),
            'supplier' => array('include_in_sitemap' => 1, 'priority' => '0.5', 'changefreq' => 'monthly'),
        );

        return isset($defaults[$page_type]) ? $defaults[$page_type] : null;
    }

    /**
     * Generate robots.txt content
     */
    public function generateRobotsTxt()
    {
        if (!Configuration::get('STPRETTYURLPRO_ROBOTS_ENABLED')) {
            return '';
        }

        $robots = "User-agent: *\n";
        
        // Allow all by default
        $robots .= "Allow: /\n\n";
        
        // Disallow admin and other sensitive areas
        $robots .= "Disallow: /admin/\n";
        $robots .= "Disallow: /classes/\n";
        $robots .= "Disallow: /config/\n";
        $robots .= "Disallow: /download/\n";
        $robots .= "Disallow: /js/\n";
        $robots .= "Disallow: /localization/\n";
        $robots .= "Disallow: /log/\n";
        $robots .= "Disallow: /mails/\n";
        $robots .= "Disallow: /modules/\n";
        $robots .= "Disallow: /pdf/\n";
        $robots .= "Disallow: /tools/\n";
        $robots .= "Disallow: /translations/\n";
        $robots .= "Disallow: /upload/\n";
        $robots .= "Disallow: /var/\n";
        $robots .= "Disallow: /vendor/\n";
        $robots .= "Disallow: /webservice/\n";
        
        // Disallow search and other dynamic pages
        $robots .= "Disallow: /*?*\n";
        $robots .= "Disallow: /*&*\n";
        $robots .= "Disallow: /search\n";
        $robots .= "Disallow: /order\n";
        $robots .= "Disallow: /cart\n";
        $robots .= "Disallow: /my-account\n";
        
        // Add sitemap reference
        if (Configuration::get('STPRETTYURLPRO_SITEMAP_ENABLED')) {
            $robots .= "\nSitemap: " . $this->base_url . "sitemap.xml\n";
        }
        
        // Add custom robots.txt content from configuration
        $custom_robots = StPrettyUrlConfig::getConfig('robots_custom_content');
        if ($custom_robots) {
            $robots .= "\n" . $custom_robots . "\n";
        }

        return $robots;
    }

    /**
     * Save sitemap to file
     */
    public function saveSitemapToFile($filename = 'sitemap.xml')
    {
        $sitemap_content = $this->generateSitemap();
        $file_path = _PS_ROOT_DIR_ . '/' . $filename;
        
        return file_put_contents($file_path, $sitemap_content) !== false;
    }

    /**
     * Save robots.txt to file
     */
    public function saveRobotsToFile($filename = 'robots.txt')
    {
        $robots_content = $this->generateRobotsTxt();
        $file_path = _PS_ROOT_DIR_ . '/' . $filename;
        
        return file_put_contents($file_path, $robots_content) !== false;
    }

    /**
     * Get sitemap statistics
     */
    public function getSitemapStats()
    {
        $stats = array(
            'total_urls' => 0,
            'categories' => 0,
            'products' => 0,
            'cms_pages' => 0,
            'manufacturers' => 0,
            'suppliers' => 0,
        );

        // Count categories
        $categories = Category::getCategories($this->context->language->id, true, false);
        foreach ($categories as $category_group) {
            $stats['categories'] += count($category_group) - 2; // Exclude root and home
        }

        // Count products
        $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'product p
                LEFT JOIN ' . _DB_PREFIX_ . 'product_shop ps ON (p.id_product = ps.id_product AND ps.id_shop = ' . (int)$this->context->shop->id . ')
                WHERE p.active = 1 AND ps.active = 1';
        $stats['products'] = (int)Db::getInstance()->getValue($sql);

        // Count CMS pages
        $cms_pages = CMS::getCMSPages($this->context->language->id, null, true);
        $stats['cms_pages'] = count($cms_pages);

        // Count manufacturers
        $manufacturers = Manufacturer::getManufacturers(false, $this->context->language->id);
        $stats['manufacturers'] = count($manufacturers);

        // Count suppliers
        $suppliers = Supplier::getSuppliers(false, $this->context->language->id);
        $stats['suppliers'] = count($suppliers);

        $stats['total_urls'] = 1 + $stats['categories'] + $stats['products'] + $stats['cms_pages'] + $stats['manufacturers'] + $stats['suppliers'];

        return $stats;
    }
}

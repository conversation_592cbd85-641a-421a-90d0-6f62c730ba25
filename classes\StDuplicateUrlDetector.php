<?php
/**
 * ST Pretty URL Pro Duplicate URL Detector Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StDuplicateUrlDetector extends ObjectModel
{
    public $id_duplicate;
    public $url;
    public $page_type;
    public $object_id;
    public $id_lang;
    public $id_shop;
    public $duplicate_count;
    public $status;
    public $date_add;
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'st_pretty_url_duplicates',
        'primary' => 'id_duplicate',
        'fields' => array(
            'url' => array('type' => self::TYPE_STRING, 'validate' => 'isUrl', 'required' => true, 'size' => 255),
            'page_type' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 50),
            'object_id' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true),
            'id_lang' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true),
            'id_shop' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt', 'required' => true),
            'duplicate_count' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'),
            'status' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 20),
            'date_add' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
            'date_upd' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
        ),
    );

    /**
     * Scan for duplicate URLs
     */
    public static function scanForDuplicates($page_type = null, $id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        // Clear existing duplicates for this scan
        self::clearDuplicates($page_type, $id_shop);

        $duplicates_found = 0;

        if (!$page_type || $page_type === 'product') {
            $duplicates_found += self::scanProductDuplicates($id_shop);
        }

        if (!$page_type || $page_type === 'category') {
            $duplicates_found += self::scanCategoryDuplicates($id_shop);
        }

        if (!$page_type || $page_type === 'cms') {
            $duplicates_found += self::scanCmsDuplicates($id_shop);
        }

        return $duplicates_found;
    }

    /**
     * Scan for product URL duplicates
     */
    private static function scanProductDuplicates($id_shop)
    {
        $duplicates_found = 0;
        $url_cleaner = StUrlCleaner::getInstance();

        $sql = 'SELECT p.id_product, pl.id_lang, pl.link_rewrite
                FROM ' . _DB_PREFIX_ . 'product p
                LEFT JOIN ' . _DB_PREFIX_ . 'product_lang pl ON (p.id_product = pl.id_product)
                LEFT JOIN ' . _DB_PREFIX_ . 'product_shop ps ON (p.id_product = ps.id_product AND ps.id_shop = ' . (int)$id_shop . ')
                WHERE p.active = 1 AND ps.active = 1
                ORDER BY pl.link_rewrite, pl.id_lang';

        $products = Db::getInstance()->executeS($sql);
        $url_counts = array();

        foreach ($products as $product) {
            $clean_url = $url_cleaner->cleanProductUrl($product['id_product'], null, $product['id_lang'], $id_shop);
            
            if ($clean_url) {
                $url_key = $clean_url . '_' . $product['id_lang'];
                
                if (!isset($url_counts[$url_key])) {
                    $url_counts[$url_key] = array(
                        'url' => $clean_url,
                        'id_lang' => $product['id_lang'],
                        'objects' => array()
                    );
                }
                
                $url_counts[$url_key]['objects'][] = array(
                    'id_product' => $product['id_product'],
                    'link_rewrite' => $product['link_rewrite']
                );
            }
        }

        // Find duplicates
        foreach ($url_counts as $url_data) {
            if (count($url_data['objects']) > 1) {
                foreach ($url_data['objects'] as $object) {
                    $duplicate = new self();
                    $duplicate->url = $url_data['url'];
                    $duplicate->page_type = 'product';
                    $duplicate->object_id = $object['id_product'];
                    $duplicate->id_lang = $url_data['id_lang'];
                    $duplicate->id_shop = $id_shop;
                    $duplicate->duplicate_count = count($url_data['objects']);
                    $duplicate->status = 'pending';
                    $duplicate->add();
                    
                    $duplicates_found++;
                }
            }
        }

        return $duplicates_found;
    }

    /**
     * Scan for category URL duplicates
     */
    private static function scanCategoryDuplicates($id_shop)
    {
        $duplicates_found = 0;
        $url_cleaner = StUrlCleaner::getInstance();

        $sql = 'SELECT c.id_category, cl.id_lang, cl.link_rewrite
                FROM ' . _DB_PREFIX_ . 'category c
                LEFT JOIN ' . _DB_PREFIX_ . 'category_lang cl ON (c.id_category = cl.id_category)
                LEFT JOIN ' . _DB_PREFIX_ . 'category_shop cs ON (c.id_category = cs.id_category AND cs.id_shop = ' . (int)$id_shop . ')
                WHERE c.active = 1 AND cs.id_shop = ' . (int)$id_shop . ' AND c.id_category > 2
                ORDER BY cl.link_rewrite, cl.id_lang';

        $categories = Db::getInstance()->executeS($sql);
        $url_counts = array();

        foreach ($categories as $category) {
            $clean_url = $url_cleaner->cleanCategoryUrl($category['id_category'], $category['id_lang'], $id_shop);
            
            if ($clean_url) {
                $url_key = $clean_url . '_' . $category['id_lang'];
                
                if (!isset($url_counts[$url_key])) {
                    $url_counts[$url_key] = array(
                        'url' => $clean_url,
                        'id_lang' => $category['id_lang'],
                        'objects' => array()
                    );
                }
                
                $url_counts[$url_key]['objects'][] = array(
                    'id_category' => $category['id_category'],
                    'link_rewrite' => $category['link_rewrite']
                );
            }
        }

        // Find duplicates
        foreach ($url_counts as $url_data) {
            if (count($url_data['objects']) > 1) {
                foreach ($url_data['objects'] as $object) {
                    $duplicate = new self();
                    $duplicate->url = $url_data['url'];
                    $duplicate->page_type = 'category';
                    $duplicate->object_id = $object['id_category'];
                    $duplicate->id_lang = $url_data['id_lang'];
                    $duplicate->id_shop = $id_shop;
                    $duplicate->duplicate_count = count($url_data['objects']);
                    $duplicate->status = 'pending';
                    $duplicate->add();
                    
                    $duplicates_found++;
                }
            }
        }

        return $duplicates_found;
    }

    /**
     * Scan for CMS URL duplicates
     */
    private static function scanCmsDuplicates($id_shop)
    {
        $duplicates_found = 0;
        $url_cleaner = StUrlCleaner::getInstance();

        $sql = 'SELECT c.id_cms, cl.id_lang, cl.link_rewrite
                FROM ' . _DB_PREFIX_ . 'cms c
                LEFT JOIN ' . _DB_PREFIX_ . 'cms_lang cl ON (c.id_cms = cl.id_cms)
                LEFT JOIN ' . _DB_PREFIX_ . 'cms_shop cs ON (c.id_cms = cs.id_cms AND cs.id_shop = ' . (int)$id_shop . ')
                WHERE c.active = 1 AND cs.id_shop = ' . (int)$id_shop . '
                ORDER BY cl.link_rewrite, cl.id_lang';

        $cms_pages = Db::getInstance()->executeS($sql);
        $url_counts = array();

        foreach ($cms_pages as $cms) {
            $clean_url = $url_cleaner->cleanCmsUrl($cms['id_cms'], $cms['id_lang'], $id_shop);
            
            if ($clean_url) {
                $url_key = $clean_url . '_' . $cms['id_lang'];
                
                if (!isset($url_counts[$url_key])) {
                    $url_counts[$url_key] = array(
                        'url' => $clean_url,
                        'id_lang' => $cms['id_lang'],
                        'objects' => array()
                    );
                }
                
                $url_counts[$url_key]['objects'][] = array(
                    'id_cms' => $cms['id_cms'],
                    'link_rewrite' => $cms['link_rewrite']
                );
            }
        }

        // Find duplicates
        foreach ($url_counts as $url_data) {
            if (count($url_data['objects']) > 1) {
                foreach ($url_data['objects'] as $object) {
                    $duplicate = new self();
                    $duplicate->url = $url_data['url'];
                    $duplicate->page_type = 'cms';
                    $duplicate->object_id = $object['id_cms'];
                    $duplicate->id_lang = $url_data['id_lang'];
                    $duplicate->id_shop = $id_shop;
                    $duplicate->duplicate_count = count($url_data['objects']);
                    $duplicate->status = 'pending';
                    $duplicate->add();
                    
                    $duplicates_found++;
                }
            }
        }

        return $duplicates_found;
    }

    /**
     * Get all duplicates
     */
    public static function getAllDuplicates($status = null, $page_type = null, $id_shop = null, $limit = null, $offset = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $sql = 'SELECT * FROM ' . _DB_PREFIX_ . 'st_pretty_url_duplicates WHERE id_shop = ' . (int)$id_shop;

        if ($status) {
            $sql .= ' AND status = "' . pSQL($status) . '"';
        }

        if ($page_type) {
            $sql .= ' AND page_type = "' . pSQL($page_type) . '"';
        }

        $sql .= ' ORDER BY duplicate_count DESC, date_add DESC';

        if ($limit) {
            $sql .= ' LIMIT ' . (int)$limit;
            if ($offset) {
                $sql .= ' OFFSET ' . (int)$offset;
            }
        }

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Clear duplicates
     */
    public static function clearDuplicates($page_type = null, $id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $sql = 'DELETE FROM ' . _DB_PREFIX_ . 'st_pretty_url_duplicates WHERE id_shop = ' . (int)$id_shop;

        if ($page_type) {
            $sql .= ' AND page_type = "' . pSQL($page_type) . '"';
        }

        return Db::getInstance()->execute($sql);
    }

    /**
     * Update duplicate status
     */
    public static function updateStatus($id_duplicate, $status)
    {
        $allowed_statuses = array('pending', 'resolved', 'ignored');
        
        if (!in_array($status, $allowed_statuses)) {
            return false;
        }

        $sql = 'UPDATE ' . _DB_PREFIX_ . 'st_pretty_url_duplicates 
                SET status = "' . pSQL($status) . '", date_upd = NOW() 
                WHERE id_duplicate = ' . (int)$id_duplicate;

        return Db::getInstance()->execute($sql);
    }

    /**
     * Get duplicate statistics
     */
    public static function getDuplicateStats($id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $stats = array(
            'total' => 0,
            'pending' => 0,
            'resolved' => 0,
            'ignored' => 0,
            'by_type' => array()
        );

        // Total count
        $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'st_pretty_url_duplicates WHERE id_shop = ' . (int)$id_shop;
        $stats['total'] = (int)Db::getInstance()->getValue($sql);

        // Count by status
        $sql = 'SELECT status, COUNT(*) as count 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_duplicates 
                WHERE id_shop = ' . (int)$id_shop . ' 
                GROUP BY status';
        
        $status_counts = Db::getInstance()->executeS($sql);
        foreach ($status_counts as $status_count) {
            $stats[$status_count['status']] = (int)$status_count['count'];
        }

        // Count by page type
        $sql = 'SELECT page_type, COUNT(*) as count 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_duplicates 
                WHERE id_shop = ' . (int)$id_shop . ' 
                GROUP BY page_type';
        
        $type_counts = Db::getInstance()->executeS($sql);
        foreach ($type_counts as $type_count) {
            $stats['by_type'][$type_count['page_type']] = (int)$type_count['count'];
        }

        return $stats;
    }

    /**
     * Get object details for duplicate
     */
    public function getObjectDetails()
    {
        switch ($this->page_type) {
            case 'product':
                $product = new Product($this->object_id, false, $this->id_lang);
                return array(
                    'name' => $product->name,
                    'reference' => $product->reference,
                    'active' => $product->active
                );

            case 'category':
                $category = new Category($this->object_id, $this->id_lang);
                return array(
                    'name' => $category->name,
                    'active' => $category->active
                );

            case 'cms':
                $cms = new CMS($this->object_id, $this->id_lang);
                return array(
                    'name' => $cms->meta_title,
                    'active' => $cms->active
                );

            default:
                return array();
        }
    }
}

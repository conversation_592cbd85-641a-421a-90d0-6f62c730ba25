<?php
/**
 * ST Pretty URL Pro Configuration Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StPrettyUrlConfig extends ObjectModel
{
    public $id_config;
    public $config_key;
    public $config_value;
    public $config_type;
    public $id_shop;
    public $id_shop_group;
    public $date_add;
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'st_pretty_url_config',
        'primary' => 'id_config',
        'fields' => array(
            'config_key' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 100),
            'config_value' => array('type' => self::TYPE_HTML, 'validate' => 'isCleanHtml'),
            'config_type' => array('type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'size' => 20),
            'id_shop' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedId'),
            'id_shop_group' => array('type' => self::TYPE_INT, 'validate' => 'isUnsignedId'),
            'date_add' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
            'date_upd' => array('type' => self::TYPE_DATE, 'validate' => 'isDate'),
        ),
    );

    /**
     * Get configuration value
     */
    public static function getConfig($key, $id_shop = null, $default = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $sql = 'SELECT config_value, config_type 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_config 
                WHERE config_key = "' . pSQL($key) . '" 
                AND (id_shop = ' . (int)$id_shop . ' OR id_shop = 0)
                ORDER BY id_shop DESC 
                LIMIT 1';

        $result = Db::getInstance()->getRow($sql);

        if ($result) {
            return self::castValue($result['config_value'], $result['config_type']);
        }

        return $default;
    }

    /**
     * Set configuration value
     */
    public static function setConfig($key, $value, $type = 'string', $id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $config = new self();
        $config->config_key = $key;
        $config->config_value = $value;
        $config->config_type = $type;
        $config->id_shop = $id_shop;
        $config->id_shop_group = (int)Context::getContext()->shop->id_shop_group;

        // Check if config already exists
        $existing_id = self::getConfigId($key, $id_shop);
        if ($existing_id) {
            $config->id = $existing_id;
            return $config->update();
        } else {
            return $config->add();
        }
    }

    /**
     * Get configuration ID
     */
    public static function getConfigId($key, $id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $sql = 'SELECT id_config 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_config 
                WHERE config_key = "' . pSQL($key) . '" 
                AND id_shop = ' . (int)$id_shop;

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Delete configuration
     */
    public static function deleteConfig($key, $id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $sql = 'DELETE FROM ' . _DB_PREFIX_ . 'st_pretty_url_config 
                WHERE config_key = "' . pSQL($key) . '" 
                AND id_shop = ' . (int)$id_shop;

        return Db::getInstance()->execute($sql);
    }

    /**
     * Cast value to proper type
     */
    private static function castValue($value, $type)
    {
        switch ($type) {
            case 'bool':
            case 'boolean':
                return (bool)$value;
            case 'int':
            case 'integer':
                return (int)$value;
            case 'float':
            case 'double':
                return (float)$value;
            case 'array':
                return json_decode($value, true);
            case 'object':
                return json_decode($value);
            default:
                return $value;
        }
    }

    /**
     * Get all configurations for a shop
     */
    public static function getAllConfigs($id_shop = null)
    {
        if ($id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $sql = 'SELECT config_key, config_value, config_type 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_config 
                WHERE id_shop = ' . (int)$id_shop . ' OR id_shop = 0
                ORDER BY config_key';

        $results = Db::getInstance()->executeS($sql);
        $configs = array();

        foreach ($results as $result) {
            $configs[$result['config_key']] = self::castValue($result['config_value'], $result['config_type']);
        }

        return $configs;
    }

    /**
     * Get URL patterns
     */
    public static function getUrlPatterns($page_type = null, $active_only = true)
    {
        $sql = 'SELECT * FROM ' . _DB_PREFIX_ . 'st_pretty_url_patterns WHERE 1';

        if ($page_type) {
            $sql .= ' AND page_type = "' . pSQL($page_type) . '"';
        }

        if ($active_only) {
            $sql .= ' AND active = 1';
        }

        $sql .= ' ORDER BY priority DESC, pattern_name ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get meta templates
     */
    public static function getMetaTemplates($page_type = null, $id_lang = null, $active_only = true)
    {
        if ($id_lang === null) {
            $id_lang = (int)Context::getContext()->language->id;
        }

        $sql = 'SELECT mt.*, mtl.* 
                FROM ' . _DB_PREFIX_ . 'st_pretty_url_meta_templates mt
                LEFT JOIN ' . _DB_PREFIX_ . 'st_pretty_url_meta_templates_lang mtl 
                ON (mt.id_meta_template = mtl.id_meta_template AND mtl.id_lang = ' . (int)$id_lang . ')
                WHERE 1';

        if ($page_type) {
            $sql .= ' AND mt.page_type = "' . pSQL($page_type) . '"';
        }

        if ($active_only) {
            $sql .= ' AND mt.active = 1';
        }

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get sitemap configuration
     */
    public static function getSitemapConfig($page_type = null, $active_only = true)
    {
        $sql = 'SELECT * FROM ' . _DB_PREFIX_ . 'st_pretty_url_sitemap WHERE 1';

        if ($page_type) {
            $sql .= ' AND page_type = "' . pSQL($page_type) . '"';
        }

        if ($active_only) {
            $sql .= ' AND active = 1';
        }

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Check if module is enabled
     */
    public static function isEnabled()
    {
        return (bool)Configuration::get('STPRETTYURLPRO_ENABLED');
    }

    /**
     * Check if ID removal is enabled
     */
    public static function isIdRemovalEnabled()
    {
        return (bool)Configuration::get('STPRETTYURLPRO_REMOVE_IDS');
    }

    /**
     * Check if auto redirects are enabled
     */
    public static function isAutoRedirectEnabled()
    {
        return (bool)Configuration::get('STPRETTYURLPRO_AUTO_REDIRECT');
    }

    /**
     * Get default redirect type
     */
    public static function getDefaultRedirectType()
    {
        return (int)Configuration::get('STPRETTYURLPRO_DEFAULT_REDIRECT_TYPE', 301);
    }

    /**
     * Check if cache is enabled
     */
    public static function isCacheEnabled()
    {
        return (bool)Configuration::get('STPRETTYURLPRO_CACHE_ENABLED');
    }

    /**
     * Check if debug mode is enabled
     */
    public static function isDebugMode()
    {
        return (bool)Configuration::get('STPRETTYURLPRO_DEBUG_MODE');
    }
}

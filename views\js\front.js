/**
 * ST Pretty URL Pro Front-end JavaScript
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

(function() {
    'use strict';

    var StPrettyUrlPro = {
        
        /**
         * Initialize module
         */
        init: function() {
            this.bindEvents();
            this.initDebugMode();
            this.trackUrlChanges();
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            // Track clicks on enhanced URLs
            document.addEventListener('click', function(e) {
                var link = e.target.closest('a');
                if (link && link.classList.contains('stprettyurlpro-enhanced')) {
                    StPrettyUrlPro.trackEnhancedClick(link);
                }
            });
        },

        /**
         * Initialize debug mode
         */
        initDebugMode: function() {
            if (typeof stprettyurlpro_debug !== 'undefined' && stprettyurlpro_debug) {
                document.body.classList.add('stprettyurlpro-debug-mode');
                this.showDebugInfo();
            }
        },

        /**
         * Show debug information
         */
        showDebugInfo: function() {
            var debugPanel = document.createElement('div');
            debugPanel.className = 'stprettyurlpro-debug';
            debugPanel.innerHTML = this.getDebugHTML();
            document.body.appendChild(debugPanel);
        },

        /**
         * Get debug HTML
         */
        getDebugHTML: function() {
            var html = '<h4>ST Pretty URL Pro Debug</h4>';
            html += '<ul>';
            html += '<li class="url-info">Current URL: ' + window.location.href + '</li>';
            html += '<li class="url-info">Clean URLs: ' + (typeof stprettyurlpro_clean_urls !== 'undefined' ? 'Enabled' : 'Disabled') + '</li>';
            html += '<li class="redirect-info">Auto Redirects: ' + (typeof stprettyurlpro_auto_redirects !== 'undefined' ? 'Enabled' : 'Disabled') + '</li>';
            html += '<li class="meta-info">Auto Meta: ' + (typeof stprettyurlpro_auto_meta !== 'undefined' ? 'Enabled' : 'Disabled') + '</li>';
            html += '</ul>';
            return html;
        },

        /**
         * Track URL changes (for SPA-like behavior)
         */
        trackUrlChanges: function() {
            var currentUrl = window.location.href;
            
            // Monitor for URL changes
            setInterval(function() {
                if (window.location.href !== currentUrl) {
                    currentUrl = window.location.href;
                    StPrettyUrlPro.onUrlChange();
                }
            }, 1000);
        },

        /**
         * Handle URL change
         */
        onUrlChange: function() {
            // Update debug info if visible
            var debugPanel = document.querySelector('.stprettyurlpro-debug');
            if (debugPanel) {
                debugPanel.innerHTML = this.getDebugHTML();
            }
        },

        /**
         * Track enhanced URL clicks
         */
        trackEnhancedClick: function(link) {
            if (typeof stprettyurlpro_track_clicks !== 'undefined' && stprettyurlpro_track_clicks) {
                // Send tracking data
                this.sendTrackingData({
                    action: 'enhanced_click',
                    url: link.href,
                    text: link.textContent.trim()
                });
            }
        },

        /**
         * Send tracking data
         */
        sendTrackingData: function(data) {
            if (typeof fetch !== 'undefined') {
                fetch(stprettyurlpro_ajax_url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                }).catch(function(error) {
                    console.log('ST Pretty URL Pro tracking error:', error);
                });
            }
        },

        /**
         * Enhance URLs on page
         */
        enhanceUrls: function() {
            var links = document.querySelectorAll('a[href]');
            links.forEach(function(link) {
                if (StPrettyUrlPro.shouldEnhanceUrl(link.href)) {
                    link.classList.add('stprettyurlpro-enhanced');
                }
            });
        },

        /**
         * Check if URL should be enhanced
         */
        shouldEnhanceUrl: function(url) {
            // Check if URL contains IDs that could be cleaned
            var patterns = [
                /\/\d+-/,  // Product/category IDs like /123-product-name
                /id_product=\d+/,  // Query parameter IDs
                /id_category=\d+/,
                /id_cms=\d+/
            ];

            return patterns.some(function(pattern) {
                return pattern.test(url);
            });
        },

        /**
         * Utility: Get clean URL for element
         */
        getCleanUrl: function(originalUrl) {
            // This would typically make an AJAX call to get the clean URL
            // For now, return the original URL
            return originalUrl;
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            StPrettyUrlPro.init();
        });
    } else {
        StPrettyUrlPro.init();
    }

    // Make available globally
    window.StPrettyUrlPro = StPrettyUrlPro;

})();
